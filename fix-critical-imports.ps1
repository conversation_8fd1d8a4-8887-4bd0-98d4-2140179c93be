# PowerShell script to fix the most critical component import paths
$blocksPath = "src\app\demo\[name]\blocks"

# Get all TypeScript files in the blocks directory
$files = Get-ChildItem -Path $blocksPath -Filter "*.tsx"

Write-Host "Found $($files.Count) files to process..."

foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw
    $originalContent = $content
    
    # Fix the most common import issues
    $content = $content -replace "'@/components/AreaChart'", "'@/components/ui/chart'"
    $content = $content -replace "'@/components/BarChart'", "'@/components/ui/chart'"
    $content = $content -replace "'@/components/DonutChart'", "'@/components/ui/chart'"
    $content = $content -replace "'@/components/Card'", "'@/components/ui/card'"
    $content = $content -replace "'@/components/Button'", "'@/components/ui/button'"
    $content = $content -replace "'@/components/Input'", "'@/components/ui/input'"
    $content = $content -replace "'@/components/Label'", "'@/components/ui/label'"
    $content = $content -replace "'@/components/Switch'", "'@/components/ui/switch'"
    $content = $content -replace "'@/components/Divider'", "'@/components/ui/separator'"
    $content = $content -replace "'@/components/Separator'", "'@/components/ui/separator'"
    $content = $content -replace "'@/components/Dialog'", "'@/components/ui/dialog'"
    $content = $content -replace "'@/components/Tabs'", "'@/components/ui/tabs'"
    $content = $content -replace "'@/components/Badge'", "'@/components/ui/badge'"
    $content = $content -replace "'@/components/Avatar'", "'@/components/ui/avatar'"
    $content = $content -replace "'@/components/Checkbox'", "'@/components/ui/checkbox'"
    $content = $content -replace "'@/components/Select'", "'@/components/ui/select'"
    $content = $content -replace "'@/components/Table'", "'@/components/ui/table'"
    $content = $content -replace "'@/components/Progress'", "'@/components/ui/progress'"
    $content = $content -replace "'@/components/ProgressBar'", "'@/components/ui/progress'"
    $content = $content -replace "'@/components/Accordion'", "'@/components/ui/accordion'"
    
    # Fix component usage in the code
    $content = $content -replace '\bDivider\b', 'Separator'
    $content = $content -replace '\bProgressBar\b', 'Progress'
    
    # Only write if content changed
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "Fixed imports in: $($file.Name)"
    }
}

Write-Host "Import fixing complete!"
