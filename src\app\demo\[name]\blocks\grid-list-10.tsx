'use client';

import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

const data = [
  //array-start
  {
    name: 'Retention – How many users return each week and month',
    description: 'Tracks user retention over weekly and monthly intervals.',
    lastEdited: '1 day ago',
    authorInitials: 'EK',
    href: '#',
  },
  {
    name: 'Revenue – By employee and role',
    description:
      'Analyzes revenue generated by employees based on their roles and contributions.',
    lastEdited: '2 days ago',
    authorInitials: 'SL',
    href: '#',
  },
  {
    name: 'Active users – Today',
    description:
      'Provides a snapshot of active users on the platform as of today.',
    lastEdited: '14 hours ago',
    authorInitials: 'AM',
    href: '#',
  },
  {
    name: 'Product Sales – Quarterly Report',
    description:
      'Details the sales performance of products over a quarterly timeframe.',
    lastEdited: '4 days ago',
    authorInitials: 'JR',
    href: '#',
  },
  {
    name: 'Customer Feedback – Survey Results',
    description:
      'Analyzes customer feedback survey results to gauge customer satisfaction.',
    lastEdited: '5 days ago',
    authorInitials: 'AC',
    href: '#',
  },
  {
    name: 'Marketing Campaign – Performance Analysis',
    description:
      'Examines the performance of marketing campaigns to return on investment (ROI).',
    lastEdited: '6 days ago',
    authorInitials: 'MS',
    href: '#',
  },
  //array-end
];

function ContentPlaceholder() {
  return (
    <div className="relative h-full overflow-hidden rounded bg-gray-50 dark:bg-gray-800">
      <svg
        className="absolute inset-0 h-full w-full stroke-gray-200 dark:stroke-gray-700"
        fill="none"
      >
        <defs>
          <pattern
            id="pattern-1"
            x="0"
            y="0"
            width="10"
            height="10"
            patternUnits="userSpaceOnUse"
          >
            <path d="M-3 13 15-5M-5 5l18-18M-1 21 17 3"></path>
          </pattern>
        </defs>
        <rect
          stroke="none"
          fill="url(#pattern-1)"
          width="100%"
          height="100%"
        ></rect>
      </svg>
    </div>
  );
}

export default function Example() {
  return (
    <div className="obfuscate">
      <div className="flex items-center space-x-2">
        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-50">
          Most recent reports
        </h3>
        <span className="inline-flex size-6 items-center justify-center rounded-full bg-gray-100 text-xs font-medium text-gray-900 dark:bg-gray-800 dark:text-gray-50">
          {data.length}
        </span>
      </div>
      <Separator className="!my-4" />
      <dl className="mt-4 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {data.map((item) => (
          <Card
            key={item.name}
            className="relative flex flex-col justify-between !p-2"
          >
            <div className="h-28">
              <ContentPlaceholder />
            </div>
            <div className="flex flex-1 flex-col px-2 pb-2 pt-3">
              <div className="flex-1">
                <dt className="truncate text-sm font-medium text-gray-900 dark:text-gray-50">
                  <a href={item.href} className="focus:outline-none">
                    {/* Extend link to entire card */}
                    <span className="absolute inset-0" aria-hidden={true} />
                    {item.name}
                  </a>
                </dt>
                <dd className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  {item.description}
                </dd>
              </div>
              <div className="mt-6 flex items-center justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-500">
                  {item.lastEdited}
                </span>
                <span
                  className="inline-flex size-7 items-center justify-center rounded-full bg-gray-100 text-xs font-medium text-gray-700 dark:bg-gray-800 dark:text-gray-300"
                  aria-hidden={true}
                >
                  {item.authorInitials}
                </span>
              </div>
            </div>
          </Card>
        ))}
      </dl>
    </div>
  );
}
