// Tremor Bar<PERSON>hart [v0.2.1]

'use client';

import React from 'react';
import {
  Bar,
  CartesianGrid,
  Label,
  BarChart as RechartsBar<PERSON><PERSON>,
  Legend as RechartsLegend,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  XAxis,
  YAxis,
} from 'recharts';
import { AxisDomain } from 'recharts/types/util/types';

import { getYAxisDomain } from '@/lib/chartUtils';
import { useOnWindowResize } from '@/lib/useOnWindowResize';
import { cx } from '@/lib/utils';

const data = [
  {
    date: 'Jan 24',
    Density: 0.891,
  },
  {
    date: 'Feb 24',
    Density: 0.084,
  },
  {
    date: 'Mar 24',
    Density: 0.155,
  },
  {
    date: 'Apr 24',
    Density: 0.75,
  },
  {
    date: 'May 24',
    Density: 0.221,
  },
  {
    date: 'Jun 24',
    Density: 0.561,
  },
  {
    date: 'Jul 24',
    Density: 0.261,
  },
  {
    date: 'Aug 24',
    Density: 0.421,
  },
];

// Tremor Custom chartColors

//array-start
type ColorUtility = 'bg' | 'stroke' | 'fill' | 'text';

const chartColors = {
  blue: {
    bg: 'bg-blue-500 dark:bg-blue-500',
    stroke: 'stroke-blue-500 dark:stroke-blue-500',
    fill: 'fill-blue-500 dark:fill-blue-500',
    text: 'text-blue-500 dark:text-blue-500',
  },
  lightBlue: {
    bg: 'bg-blue-300/50 dark:bg-blue-800/50',
    stroke: 'stroke-blue-300/50 dark:stroke-blue-800/50',
    fill: 'fill-blue-300/50 dark:fill-blue-800/50',
    text: 'text-blue-300/50 dark:text-blue-800/50',
  },
  emerald: {
    bg: 'bg-emerald-500 dark:bg-emerald-500',
    stroke: 'stroke-emerald-500 dark:stroke-emerald-500',
    fill: 'fill-emerald-500 dark:fill-emerald-500',
    text: 'text-emerald-500 dark:text-emerald-500',
  },
  lightEmerald: {
    bg: 'bg-emerald-300/50 dark:bg-emerald-800/50',
    stroke: 'stroke-emerald-300/50 dark:stroke-emerald-800/50',
    fill: 'fill-emerald-300/50 dark:fill-emerald-800/50',
    text: 'text-emerald-300/50 dark:text-emerald-800/50',
  },
  violet: {
    bg: 'bg-violet-500 dark:bg-violet-500',
    stroke: 'stroke-violet-500 dark:stroke-violet-500',
    fill: 'fill-violet-500 dark:fill-violet-500',
    text: 'text-violet-500 dark:text-violet-500',
  },
  amber: {
    bg: 'bg-amber-500 dark:bg-amber-500',
    stroke: 'stroke-amber-500 dark:stroke-amber-500',
    fill: 'fill-amber-500 dark:fill-amber-500',
    text: 'text-amber-500 dark:text-amber-500',
  },
  gray: {
    bg: 'bg-gray-400 dark:bg-gray-600',
    stroke: 'stroke-gray-400 dark:stroke-gray-600',
    fill: 'fill-gray-400 dark:fill-gray-600',
    text: 'text-gray-400 dark:text-gray-600',
  },
  rose: {
    bg: 'bg-rose-600 dark:bg-rose-500',
    stroke: 'stroke-rose-600 dark:stroke-rose-500',
    fill: 'fill-rose-600 dark:fill-rose-500',
    text: 'text-rose-600 dark:text-rose-500',
  },
  sky: {
    bg: 'bg-sky-500 dark:bg-sky-500',
    stroke: 'stroke-sky-500 dark:stroke-sky-500',
    fill: 'fill-sky-500 dark:fill-sky-500',
    text: 'text-sky-500 dark:text-sky-500',
  },
  cyan: {
    bg: 'bg-cyan-500 dark:bg-cyan-500',
    stroke: 'stroke-cyan-500 dark:stroke-cyan-500',
    fill: 'fill-cyan-500 dark:fill-cyan-500',
    text: 'text-cyan-500 dark:text-cyan-500',
  },
  indigo: {
    bg: 'bg-indigo-600 dark:bg-indigo-500',
    stroke: 'stroke-indigo-600 dark:stroke-indigo-500',
    fill: 'fill-indigo-600 dark:fill-indigo-500',
    text: 'text-indigo-600 dark:text-indigo-500',
  },
  orange: {
    bg: 'bg-orange-500 dark:bg-orange-400',
    stroke: 'stroke-orange-500 dark:stroke-orange-400',
    fill: 'fill-orange-500 dark:fill-orange-400',
    text: 'text-orange-500 dark:text-orange-400',
  },
  pink: {
    bg: 'bg-pink-500 dark:bg-pink-500',
    stroke: 'stroke-pink-500 dark:stroke-pink-500',
    fill: 'fill-pink-500 dark:fill-pink-500',
    text: 'text-pink-500 dark:text-pink-500',
  },
  red: {
    bg: 'bg-red-500 dark:bg-red-500',
    stroke: 'stroke-red-500 dark:stroke-red-500',
    fill: 'fill-red-500 dark:fill-red-500',
    text: 'text-red-500 dark:text-red-500',
  },
  lightGray: {
    bg: 'bg-gray-300 dark:bg-gray-700',
    stroke: 'stroke-gray-300 dark:stroke-gray-700',
    fill: 'fill-gray-300 dark:fill-gray-700',
    text: 'text-gray-300 dark:text-gray-700',
  },
} as const satisfies {
  [color: string]: {
    [key in ColorUtility]: string;
  };
};

type AvailableChartColorsKeys = keyof typeof chartColors;

const chartGradientColors = {
  blue: 'from-blue-200 to-blue-500 dark:from-blue-200/10 dark:to-blue-400',
  lightBlue: 'from-blue-200 to-blue-500 dark:from-blue-200/10 dark:to-blue-400',
  emerald:
    'from-emerald-200 to-emerald-500 dark:from-emerald-200/10 dark:to-emerald-400',
  lightEmerald:
    'from-emerald-200 to-emerald-500 dark:from-emerald-200/10 dark:to-emerald-400',
  violet:
    'from-violet-200 to-violet-500 dark:from-violet-200/10 dark:to-violet-400',
  amber: 'from-amber-200 to-amber-500 dark:from-amber-200/10 dark:to-amber-400',
  gray: 'from-gray-200 to-gray-500 dark:from-gray-200/10 dark:to-gray-400',
  lightGray: 'from-gray-200 to-gray-500 dark:from-gray-200/10 dark:to-gray-400',
  rose: 'from-rose-200 to-rose-500 dark:from-rose-200/10 dark:to-rose-400',
  sky: 'from-sky-200 to-sky-500 dark:from-sky-200/10 dark:to-sky-400',
  cyan: 'from-cyan-200 to-cyan-500 dark:from-cyan-200/10 dark:to-cyan-400',
  indigo:
    'from-indigo-200 to-indigo-500 dark:from-indigo-200/10 dark:to-indigo-400',
  orange:
    'from-orange-200 to-orange-500 dark:from-orange-200/10 dark:to-orange-400',
  pink: 'from-pink-200 to-pink-500 dark:from-pink-200/10 dark:to-pink-400',
  red: 'from-red-200 to-red-500 dark:from-red-200/10 dark:to-red-400',
} as const satisfies Record<string, string>;

const chartConditionalColors = {
  blue: {
    low: 'fill-blue-200 dark:fill-blue-300',
    medium: 'fill-blue-300 dark:fill-blue-400',
    high: 'fill-blue-400 dark:fill-blue-500',
    critical: 'fill-blue-500 dark:fill-blue-600',
  },
  lightBlue: {
    low: 'fill-blue-200 dark:fill-blue-300',
    medium: 'fill-blue-300 dark:fill-blue-400',
    high: 'fill-blue-400 dark:fill-blue-500',
    critical: 'fill-blue-500 dark:fill-blue-600',
  },
  emerald: {
    low: 'fill-emerald-200 dark:fill-emerald-300',
    medium: 'fill-emerald-300 dark:fill-emerald-400',
    high: 'fill-emerald-400 dark:fill-emerald-500',
    critical: 'fill-emerald-500 dark:fill-emerald-600',
  },
  lightEmerald: {
    low: 'fill-emerald-200 dark:fill-emerald-300',
    medium: 'fill-emerald-300 dark:fill-emerald-400',
    high: 'fill-emerald-400 dark:fill-emerald-500',
    critical: 'fill-emerald-500 dark:fill-emerald-600',
  },
  violet: {
    low: 'fill-violet-200 dark:fill-violet-300',
    medium: 'fill-violet-300 dark:fill-violet-400',
    high: 'fill-violet-400 dark:fill-violet-500',
    critical: 'fill-violet-500 dark:fill-violet-600',
  },
  amber: {
    low: 'fill-amber-200 dark:fill-amber-300',
    medium: 'fill-amber-300 dark:fill-amber-400',
    high: 'fill-amber-400 dark:fill-amber-500',
    critical: 'fill-amber-500 dark:fill-amber-600',
  },
  gray: {
    low: 'fill-gray-200 dark:fill-gray-300',
    medium: 'fill-gray-300 dark:fill-gray-400',
    high: 'fill-gray-400 dark:fill-gray-500',
    critical: 'fill-gray-500 dark:fill-gray-600',
  },
  rose: {
    low: 'fill-rose-200 dark:fill-rose-300',
    medium: 'fill-rose-300 dark:fill-rose-400',
    high: 'fill-rose-400 dark:fill-rose-500',
    critical: 'fill-rose-500 dark:fill-rose-600',
  },
  sky: {
    low: 'fill-sky-200 dark:fill-sky-300',
    medium: 'fill-sky-300 dark:fill-sky-400',
    high: 'fill-sky-400 dark:fill-sky-500',
    critical: 'fill-sky-500 dark:fill-sky-600',
  },
  cyan: {
    low: 'fill-cyan-200 dark:fill-cyan-300',
    medium: 'fill-cyan-300 dark:fill-cyan-400',
    high: 'fill-cyan-400 dark:fill-cyan-500',
    critical: 'fill-cyan-500 dark:fill-cyan-600',
  },
  indigo: {
    low: 'fill-indigo-200 dark:fill-indigo-300',
    medium: 'fill-indigo-300 dark:fill-indigo-400',
    high: 'fill-indigo-400 dark:fill-indigo-500',
    critical: 'fill-indigo-500 dark:fill-indigo-600',
  },
  orange: {
    low: 'fill-orange-200 dark:fill-orange-300',
    medium: 'fill-orange-300 dark:fill-orange-400',
    high: 'fill-orange-400 dark:fill-orange-500',
    critical: 'fill-orange-500 dark:fill-orange-600',
  },
  pink: {
    low: 'fill-pink-200 dark:fill-pink-300',
    medium: 'fill-pink-300 dark:fill-pink-400',
    high: 'fill-pink-400 dark:fill-pink-500',
    critical: 'fill-pink-500 dark:fill-pink-600',
  },
  red: {
    low: 'fill-red-200 dark:fill-red-300',
    medium: 'fill-red-300 dark:fill-red-400',
    high: 'fill-red-400 dark:fill-red-500',
    critical: 'fill-red-500 dark:fill-red-600',
  },
  lightGray: {
    low: 'fill-gray-200 dark:fill-gray-300',
    medium: 'fill-gray-300 dark:fill-gray-400',
    high: 'fill-gray-400 dark:fill-gray-500',
    critical: 'fill-gray-500 dark:fill-gray-600',
  },
};

type AvailableChartConditionalColorsKeys = keyof typeof chartColors;

const AvailableChartColors: AvailableChartColorsKeys[] = Object.keys(
  chartColors,
) as Array<AvailableChartColorsKeys>;

const constructCategoryColors = (
  categories: string[],
  colors: AvailableChartColorsKeys[],
): Map<string, AvailableChartColorsKeys> => {
  const categoryColors = new Map<string, AvailableChartColorsKeys>();
  categories.forEach((category, index) => {
    categoryColors.set(category, colors[index % colors.length]);
  });
  return categoryColors;
};

const getColorClassName = (
  color: AvailableChartColorsKeys,
  type: ColorUtility,
): string => {
  const fallbackColor = {
    bg: 'bg-gray-500',
    stroke: 'stroke-gray-500',
    fill: 'fill-gray-500',
    text: 'text-gray-500',
  };
  return chartColors[color]?.[type] ?? fallbackColor[type];
};

const getGradientColorClassName = (color: AvailableChartColorsKeys): string => {
  return chartGradientColors[color];
};

const getConditionalColorClassName = (
  value: number,
  color: AvailableChartConditionalColorsKeys,
) => {
  const fallbackColors = {
    low: 'fill-gray-300 dark:fill-gray-400',
    medium: 'fill-gray-400 dark:fill-gray-500',
    high: 'fill-gray-500 dark:fill-gray-600',
    critical: 'fill-gray-600 dark:fill-gray-700',
  };

  const classes = chartConditionalColors[color] ?? fallbackColors;

  if (value <= 0.25) return classes.low;
  if (value <= 0.5) return classes.medium;
  if (value <= 0.75) return classes.high;
  return classes.critical;
};

//array-end

function formatPercentage({
  number,
  decimals = 1,
}: {
  number: number;
  decimals?: number;
}): string {
  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(number);
}

const CustomTooltip = ({ payload, active }: TooltipProps) => {
  if (!active || !payload || payload.length === 0) return null;

  {
    /* dummy values for showcase */
  }
  const PEER_AVERAGE = 0.75;

  const calculateDiff = () => {
    const difference = payload[0].value - PEER_AVERAGE;
    const sign = difference > 0 ? '+' : '';
    return `${sign}${formatPercentage({ number: difference })}`;
  };

  const peerDifference = calculateDiff();

  return (
    <div className="w-56 rounded-md border border-gray-200 bg-white text-sm shadow-md dark:border-gray-800 dark:bg-gray-950">
      <ul role="list" className="grid grid-cols-2 gap-x-4 p-2">
        <li className="flex space-x-2.5">
          <span
            className={cx(
              `bg-${payload[0].color}-500 dark:bg-${payload[0].color}-500`,
              'w-1 rounded',
            )}
            aria-hidden="true"
          />
          <div className="space-y-0.5">
            <p className="whitespace-nowrap text-xs text-gray-500 dark:text-gray-500">
              {payload[0].category}
            </p>
            <p className="font-medium text-gray-900 dark:text-gray-50">
              {formatPercentage({ number: payload[0].value })}
            </p>
          </div>
        </li>
        <li className="flex space-x-2.5">
          <span
            className="w-1 rounded bg-gray-400 dark:bg-gray-600"
            aria-hidden="true"
          />
          <div className="space-y-0.5">
            <p className="whitespace-nowrap text-xs text-gray-500 dark:text-gray-500">
              Benchmark
            </p>
            <p className="font-medium text-gray-900 dark:text-gray-50">
              {formatPercentage({ number: PEER_AVERAGE })}
            </p>
          </div>
        </li>
      </ul>
      <div className="border-t border-gray-200 p-2 dark:border-gray-800">
        <p className="inline-flex w-full justify-center rounded bg-gray-100 px-1.5 py-1 text-xs text-gray-600 dark:bg-gray-400/20 dark:text-gray-400">
          <span className="mr-1">{peerDifference}</span>
          {parseFloat(peerDifference) > 0
            ? 'above benchmark'
            : parseFloat(peerDifference) === 0
              ? 'same as benchmark'
              : 'below benchmark'}
        </p>
      </div>
    </div>
  );
};

//#region Shape

function deepEqual<T>(obj1: T, obj2: T): boolean {
  if (obj1 === obj2) return true;

  if (
    typeof obj1 !== 'object' ||
    typeof obj2 !== 'object' ||
    obj1 === null ||
    obj2 === null
  ) {
    return false;
  }

  const keys1 = Object.keys(obj1) as Array<keyof T>;
  const keys2 = Object.keys(obj2) as Array<keyof T>;

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
}

const renderShape = (
  props: any,
  activeBar: any | undefined,
  activeLegend: string | undefined,
  layout: string,
  color: AvailableChartColorsKeys,
) => {
  const { fillOpacity, name, payload, value } = props;
  let { x, width, y, height } = props;

  if (layout === 'horizontal' && height < 0) {
    y += height;
    height = Math.abs(height); // height must be a positive number
  } else if (layout === 'vertical' && width < 0) {
    x += width;
    width = Math.abs(width); // width must be a positive number
  }

  return (
    <>
      <rect
        x={x}
        y={y}
        width={width}
        height={height}
        className={getConditionalColorClassName(value, color)}
        opacity={
          activeBar || (activeLegend && activeLegend !== name)
            ? deepEqual(activeBar, { ...payload, value })
              ? fillOpacity
              : 0.3
            : fillOpacity
        }
      />
    </>
  );
};

//#region Legend

interface LegendItemProps {
  name: string;
  color: AvailableChartColorsKeys;
  onClick?: (name: string, color: AvailableChartColorsKeys) => void;
}

const LegendItem = ({ name, color, onClick }: LegendItemProps) => {
  const hasOnValueChange = !!onClick;
  return (
    <div
      className={cx(
        // base
        'group inline-flex flex-nowrap items-center gap-2 whitespace-nowrap rounded px-2 py-1 transition',
        hasOnValueChange
          ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800'
          : 'cursor-default',
      )}
      onClick={(e) => {
        e.stopPropagation();
        onClick?.(name, color);
      }}
    >
      <span className="text-xs text-gray-700 dark:text-gray-300">Low</span>
      <span
        className={cx(
          getGradientColorClassName(color),
          'h-1.5 w-14 rounded-full bg-gradient-to-r',
        )}
      />
      <span className="text-xs text-gray-700 dark:text-gray-300">High</span>
    </div>
  );
};

interface LegendProps extends React.OlHTMLAttributes<HTMLOListElement> {
  categories: string[];
  colors?: AvailableChartColorsKeys[];
  onClickLegendItem?: (category: string, color: string) => void;
  activeLegend?: string;
}

const Legend = React.forwardRef<HTMLOListElement, LegendProps>((props, ref) => {
  const {
    categories,
    colors = AvailableChartColors,
    className,
    onClickLegendItem,
    activeLegend,
    ...other
  } = props;
  return (
    <ol
      ref={ref}
      className={cx('relative overflow-hidden', className)}
      {...other}
    >
      <div tabIndex={0} className="flex h-full flex-wrap">
        {categories.map((category, index) => (
          <LegendItem
            key={`item-${index}`}
            name={category}
            color={colors[index] as AvailableChartColorsKeys}
            onClick={onClickLegendItem}
          />
        ))}
      </div>
    </ol>
  );
});

Legend.displayName = 'Legend';

const ChartLegend = (
  { payload }: any,
  categoryColors: Map<string, AvailableChartColorsKeys>,
  setLegendHeight: React.Dispatch<React.SetStateAction<number>>,
  activeLegend: string | undefined,
  onClick?: (category: string, color: string) => void,
  legendPosition?: 'left' | 'center' | 'right',
  yAxisWidth?: number,
) => {
  const legendRef = React.useRef<HTMLDivElement>(null);

  useOnWindowResize(() => {
    const calculateHeight = (height: number | undefined) =>
      height ? Number(height) + 15 : 60;
    setLegendHeight(calculateHeight(legendRef.current?.clientHeight));
  });

  const filteredPayload = payload.filter((item: any) => item.type !== 'none');

  const paddingLeft =
    legendPosition === 'left' && yAxisWidth ? yAxisWidth - 8 : 0;

  return (
    <div
      style={{ paddingLeft: paddingLeft }}
      ref={legendRef}
      className={cx(
        'flex items-center',
        { 'justify-center': legendPosition === 'center' },
        {
          'justify-start': legendPosition === 'left',
        },
        { 'justify-end': legendPosition === 'right' },
      )}
    >
      <Legend
        categories={filteredPayload.map((entry: any) => entry.value)}
        colors={filteredPayload.map((entry: any) =>
          categoryColors.get(entry.value),
        )}
        onClickLegendItem={onClick}
        activeLegend={activeLegend}
      />
    </div>
  );
};

//#region Tooltip

type TooltipProps = Pick<ChartTooltipProps, 'active' | 'payload' | 'label'>;

type PayloadItem = {
  category: string;
  value: number;
  index: string;
  color: AvailableChartColorsKeys;
  type?: string;
  payload: any;
};

interface ChartTooltipProps {
  active: boolean | undefined;
  payload: PayloadItem[];
  label: string;
  valueFormatter: (value: number) => string;
}

const ChartTooltip = ({
  active,
  payload,
  label,
  valueFormatter,
}: ChartTooltipProps) => {
  if (active && payload && payload.length) {
    return (
      <div
        className={cx(
          // base
          'rounded-md border text-sm shadow-md',
          // border color
          'border-gray-200 dark:border-gray-800',
          // background color
          'bg-white dark:bg-gray-950',
        )}
      >
        <div className={cx('border-b border-inherit px-4 py-2')}>
          <p
            className={cx(
              // base
              'font-medium',
              // text color
              'text-gray-900 dark:text-gray-50',
            )}
          >
            {label}
          </p>
        </div>
        <div className={cx('space-y-1 px-4 py-2')}>
          {payload.map(({ value, category, color }, index) => (
            <div
              key={`id-${index}`}
              className="flex items-center justify-between space-x-8"
            >
              <div className="flex items-center space-x-2">
                <span
                  aria-hidden="true"
                  className={cx(
                    'size-2 shrink-0 rounded-sm',
                    getColorClassName(color, 'bg'),
                  )}
                />
                <p
                  className={cx(
                    // base
                    'whitespace-nowrap text-right',
                    // text color
                    'text-gray-700 dark:text-gray-300',
                  )}
                >
                  {category}
                </p>
              </div>
              <p
                className={cx(
                  // base
                  'whitespace-nowrap text-right font-medium tabular-nums',
                  // text color
                  'text-gray-900 dark:text-gray-50',
                )}
              >
                {valueFormatter(value)}
              </p>
            </div>
          ))}
        </div>
      </div>
    );
  }
  return null;
};

//#region BarChart

type BaseEventProps = {
  eventType: 'category' | 'bar';
  categoryClicked: string;
  [key: string]: number | string;
};

type BarChartEventProps = BaseEventProps | null | undefined;

interface BarChartProps extends React.HTMLAttributes<HTMLDivElement> {
  data: Record<string, any>[];
  index: string;
  categories: string[];
  colors?: AvailableChartColorsKeys[];
  valueFormatter?: (value: number) => string;
  startEndOnly?: boolean;
  showXAxis?: boolean;
  showYAxis?: boolean;
  showGridLines?: boolean;
  yAxisWidth?: number;
  intervalType?: 'preserveStartEnd' | 'equidistantPreserveStart';
  showTooltip?: boolean;
  showLegend?: boolean;
  autoMinValue?: boolean;
  minValue?: number;
  maxValue?: number;
  allowDecimals?: boolean;
  onValueChange?: (value: BarChartEventProps) => void;
  enableLegendSlider?: boolean;
  tickGap?: number;
  barCategoryGap?: string | number;
  xAxisLabel?: string;
  yAxisLabel?: string;
  layout?: 'vertical' | 'horizontal';
  type?: 'default' | 'stacked' | 'percent';
  legendPosition?: 'left' | 'center' | 'right';
  tooltipCallback?: (tooltipCallbackContent: TooltipProps) => void;
  customTooltip?: React.ComponentType<TooltipProps>;
}

const ConditionalBarChart = React.forwardRef<HTMLDivElement, BarChartProps>(
  (props, forwardedRef) => {
    const {
      data = [],
      categories = [],
      index,
      colors = AvailableChartColors,
      valueFormatter = (value: number) => value.toString(),
      startEndOnly = false,
      showXAxis = true,
      showYAxis = true,
      showGridLines = true,
      yAxisWidth = 56,
      intervalType = 'equidistantPreserveStart',
      showTooltip = true,
      showLegend = true,
      autoMinValue = false,
      minValue,
      maxValue,
      allowDecimals = true,
      className,
      onValueChange,
      enableLegendSlider = false,
      barCategoryGap,
      tickGap = 5,
      xAxisLabel,
      yAxisLabel,
      layout = 'horizontal',
      type = 'default',
      legendPosition = 'right',
      tooltipCallback,
      customTooltip,
      ...other
    } = props;
    const CustomTooltip = customTooltip;
    const paddingValue =
      (!showXAxis && !showYAxis) || (startEndOnly && !showYAxis) ? 0 : 20;
    const [legendHeight, setLegendHeight] = React.useState(60);
    const [activeLegend, setActiveLegend] = React.useState<string | undefined>(
      undefined,
    );
    const categoryColors = constructCategoryColors(categories, colors);
    const [activeBar, setActiveBar] = React.useState<any | undefined>(
      undefined,
    );
    const yAxisDomain = getYAxisDomain(autoMinValue, minValue, maxValue);
    const hasOnValueChange = !!onValueChange;
    const stacked = type === 'stacked' || type === 'percent';

    const prevActiveRef = React.useRef<boolean | undefined>(undefined);
    const prevLabelRef = React.useRef<string | undefined>(undefined);

    function valueToPercent(value: number) {
      return `${(value * 100).toFixed(0)}%`;
    }

    function onBarClick(data: any, _: any, event: React.MouseEvent) {
      event.stopPropagation();
      if (!onValueChange) return;
      if (deepEqual(activeBar, { ...data.payload, value: data.value })) {
        setActiveLegend(undefined);
        setActiveBar(undefined);
        onValueChange?.(null);
      } else {
        setActiveLegend(data.tooltipPayload?.[0]?.dataKey);
        setActiveBar({
          ...data.payload,
          value: data.value,
        });
        onValueChange?.({
          eventType: 'bar',
          categoryClicked: data.tooltipPayload?.[0]?.dataKey,
          ...data.payload,
        });
      }
    }

    function onCategoryClick(dataKey: string) {
      if (!hasOnValueChange) return;
      if (dataKey === activeLegend && !activeBar) {
        setActiveLegend(undefined);
        onValueChange?.(null);
      } else {
        setActiveLegend(dataKey);
        onValueChange?.({
          eventType: 'category',
          categoryClicked: dataKey,
        });
      }
      setActiveBar(undefined);
    }

    return (
      <div
        ref={forwardedRef}
        className={cx('h-80 w-full', className)}
        tremor-id="tremor-raw"
        {...other}
      >
        <ResponsiveContainer>
          <RechartsBarChart
            data={data}
            onClick={
              hasOnValueChange && (activeLegend || activeBar)
                ? () => {
                    setActiveBar(undefined);
                    setActiveLegend(undefined);
                    onValueChange?.(null);
                  }
                : undefined
            }
            margin={{
              bottom: xAxisLabel ? 30 : undefined,
              left: yAxisLabel ? 20 : undefined,
              right: yAxisLabel ? 5 : undefined,
              top: 5,
            }}
            stackOffset={type === 'percent' ? 'expand' : undefined}
            layout={layout}
            barCategoryGap={barCategoryGap}
          >
            {showGridLines ? (
              <CartesianGrid
                className={cx('stroke-gray-200 stroke-1 dark:stroke-gray-800')}
                horizontal={layout !== 'vertical'}
                vertical={layout === 'vertical'}
              />
            ) : null}
            <XAxis
              hide={!showXAxis}
              tick={{
                transform:
                  layout !== 'vertical' ? 'translate(0, 6)' : undefined,
              }}
              fill=""
              stroke=""
              className={cx(
                // base
                'text-xs',
                // text fill
                'fill-gray-500 dark:fill-gray-500',
                { 'mt-4': layout !== 'vertical' },
              )}
              tickLine={false}
              axisLine={false}
              minTickGap={tickGap}
              {...(layout !== 'vertical'
                ? {
                    padding: {
                      left: paddingValue,
                      right: paddingValue,
                    },
                    dataKey: index,
                    interval: startEndOnly ? 'preserveStartEnd' : intervalType,
                    ticks: startEndOnly
                      ? [data[0][index], data[data.length - 1][index]]
                      : undefined,
                  }
                : {
                    type: 'number',
                    domain: yAxisDomain as AxisDomain,
                    tickFormatter:
                      type === 'percent' ? valueToPercent : valueFormatter,
                    allowDecimals: allowDecimals,
                  })}
            >
              {xAxisLabel && (
                <Label
                  position="insideBottom"
                  offset={-20}
                  className="fill-gray-800 text-sm font-medium dark:fill-gray-200"
                >
                  {xAxisLabel}
                </Label>
              )}
            </XAxis>
            <YAxis
              width={yAxisWidth}
              hide={!showYAxis}
              axisLine={false}
              tickLine={false}
              fill=""
              stroke=""
              className={cx(
                // base
                'text-xs',
                // text fill
                'fill-gray-500 dark:fill-gray-500',
              )}
              tick={{
                transform:
                  layout !== 'vertical'
                    ? 'translate(-3, 0)'
                    : 'translate(0, 0)',
              }}
              {...(layout !== 'vertical'
                ? {
                    type: 'number',
                    domain: yAxisDomain as AxisDomain,
                    tickFormatter:
                      type === 'percent' ? valueToPercent : valueFormatter,
                    allowDecimals: allowDecimals,
                  }
                : {
                    dataKey: index,
                    ticks: startEndOnly
                      ? [data[0][index], data[data.length - 1][index]]
                      : undefined,
                    type: 'category',
                    interval: 'equidistantPreserveStart',
                  })}
            >
              {yAxisLabel && (
                <Label
                  position="insideLeft"
                  style={{ textAnchor: 'middle' }}
                  angle={-90}
                  offset={-10}
                  className="fill-gray-500 text-xs font-normal dark:fill-gray-500"
                >
                  {yAxisLabel}
                </Label>
              )}
            </YAxis>
            <Tooltip
              wrapperStyle={{ outline: 'none' }}
              isAnimationActive={true}
              animationDuration={100}
              cursor={{ fill: '#d1d5db', opacity: '0.15' }}
              offset={20}
              position={{
                y: layout === 'horizontal' ? 0 : undefined,
                x: layout === 'horizontal' ? undefined : yAxisWidth + 20,
              }}
              content={({ active, payload, label }) => {
                const cleanPayload: TooltipProps['payload'] = payload
                  ? payload.map((item: any) => ({
                      category: item.dataKey,
                      value: item.value,
                      index: item.payload[index],
                      color: categoryColors.get(
                        item.dataKey,
                      ) as AvailableChartColorsKeys,
                      type: item.type,
                      payload: item.payload,
                    }))
                  : [];

                if (
                  tooltipCallback &&
                  (active !== prevActiveRef.current ||
                    label !== prevLabelRef.current)
                ) {
                  tooltipCallback({ active, payload: cleanPayload, label });
                  prevActiveRef.current = active;
                  prevLabelRef.current = label;
                }

                return showTooltip && active ? (
                  CustomTooltip ? (
                    <CustomTooltip
                      active={active}
                      payload={cleanPayload}
                      label={label}
                    />
                  ) : (
                    <ChartTooltip
                      active={active}
                      payload={cleanPayload}
                      label={label}
                      valueFormatter={valueFormatter}
                    />
                  )
                ) : null;
              }}
            />
            {showLegend ? (
              <RechartsLegend
                verticalAlign="top"
                height={legendHeight}
                content={({ payload }) =>
                  ChartLegend(
                    { payload },
                    categoryColors,
                    setLegendHeight,
                    activeLegend,
                    hasOnValueChange
                      ? (clickedLegendItem: string) =>
                          onCategoryClick(clickedLegendItem)
                      : undefined,
                    legendPosition,
                    yAxisWidth,
                  )
                }
              />
            ) : null}
            {categories.map((category) => (
              <Bar
                className={cx(onValueChange ? 'cursor-pointer' : '')}
                key={category}
                name={category}
                type="linear"
                dataKey={category}
                stackId={stacked ? 'stack' : undefined}
                isAnimationActive={false}
                fill=""
                shape={(props: any) =>
                  renderShape(
                    props,
                    activeBar,
                    activeLegend,
                    layout,
                    categoryColors.get(category) as AvailableChartColorsKeys,
                  )
                }
                onClick={onBarClick}
              />
            ))}
          </RechartsBarChart>
        </ResponsiveContainer>
      </div>
    );
  },
);

ConditionalBarChart.displayName = 'ConditionalBarChart';

export { ConditionalBarChart, type BarChartEventProps, type TooltipProps };

export default function Example() {
  return (
    <div className="flex flex-col justify-between">
      <div>
        <dt className="text-sm font-semibold text-gray-900 dark:text-gray-50">
          Bidder density
        </dt>
        <dd className="mt-0.5 text-sm/6 text-gray-500 dark:text-gray-500">
          Competition level measured by number and size of bidders over time
        </dd>
      </div>
      <ConditionalBarChart
        data={data}
        index="date"
        categories={['Density']}
        colors={['orange']}
        customTooltip={CustomTooltip}
        valueFormatter={(value) =>
          formatPercentage({ number: value, decimals: 0 })
        }
        yAxisWidth={55}
        yAxisLabel="Competition density (%)"
        barCategoryGap="30%"
        className="mt-4 hidden h-60 md:block"
      />
      <ConditionalBarChart
        data={data}
        index="date"
        categories={['Density']}
        colors={['orange']}
        customTooltip={CustomTooltip}
        valueFormatter={(value) =>
          formatPercentage({ number: value, decimals: 0 })
        }
        showYAxis={false}
        barCategoryGap="30%"
        className="mt-4 h-60 md:hidden"
      />
    </div>
  );
}
