# PowerShell script to fix ALL component import paths in blocks
Write-Host "Starting comprehensive import fix..."

# Get all TypeScript files in the blocks directory using literal path
$blocksPath = "src\app\demo\`[name`]\blocks"
$files = @()

# Try different approaches to get the files
try {
    $files = Get-ChildItem -Path $blocksPath -Filter "*.tsx" -ErrorAction Stop
} catch {
    Write-Host "First attempt failed, trying alternative path..."
    try {
        $files = Get-ChildItem -Path "src/app/demo/[name]/blocks" -Filter "*.tsx" -ErrorAction Stop
    } catch {
        Write-Host "Second attempt failed, trying direct enumeration..."
        $files = Get-ChildItem -Path "src" -Recurse -Filter "*.tsx" | Where-Object { $_.FullName -like "*demo*blocks*" }
    }
}

Write-Host "Found $($files.Count) files to process..."

if ($files.Count -eq 0) {
    Write-Host "No files found. Listing directory contents..."
    Get-ChildItem -Path "src" -Recurse | Where-Object { $_.Name -like "*blocks*" } | ForEach-Object { Write-Host $_.FullName }
    exit
}

# Component mapping from incorrect to correct paths
$importMappings = @{
    "@/components/Accordion" = "@/components/ui/accordion"
    "@/components/Alert" = "@/components/ui/alert"
    "@/components/AlertDialog" = "@/components/ui/alert-dialog"
    "@/components/AreaChart" = "@/components/ui/chart"
    "@/components/Avatar" = "@/components/ui/avatar"
    "@/components/Badge" = "@/components/ui/badge"
    "@/components/BarChart" = "@/components/ui/chart"
    "@/components/Button" = "@/components/ui/button"
    "@/components/Calendar" = "@/components/ui/calendar"
    "@/components/Card" = "@/components/ui/card"
    "@/components/CategoryBar" = "@/components/ui/chart"
    "@/components/Chart" = "@/components/ui/chart"
    "@/components/Checkbox" = "@/components/ui/checkbox"
    "@/components/ComboChart" = "@/components/ui/chart"
    "@/components/DatePicker" = "@/components/ui/calendar"
    "@/components/Dialog" = "@/components/ui/dialog"
    "@/components/Divider" = "@/components/ui/separator"
    "@/components/DonutChart" = "@/components/ui/chart"
    "@/components/Drawer" = "@/components/ui/drawer"
    "@/components/DropdownMenu" = "@/components/ui/dropdown-menu"
    "@/components/Input" = "@/components/ui/input"
    "@/components/Label" = "@/components/ui/label"
    "@/components/LineChart" = "@/components/ui/chart"
    "@/components/PieChart" = "@/components/ui/chart"
    "@/components/Progress" = "@/components/ui/progress"
    "@/components/ProgressBar" = "@/components/ui/progress"
    "@/components/Select" = "@/components/ui/select"
    "@/components/Separator" = "@/components/ui/separator"
    "@/components/Switch" = "@/components/ui/switch"
    "@/components/Table" = "@/components/ui/table"
    "@/components/Tabs" = "@/components/ui/tabs"
    "@/components/Tooltip" = "@/components/ui/tooltip"
    "@/components/Tracker" = "@/components/ui/chart"
}

# Component name mappings for usage in code
$componentMappings = @{
    "Divider" = "Separator"
    "ProgressBar" = "Progress"
}

$totalFixed = 0

foreach ($file in $files) {
    Write-Host "Processing: $($file.Name)"
    $content = Get-Content -Path $file.FullName -Raw
    $originalContent = $content
    
    # Fix import statements
    foreach ($oldImport in $importMappings.Keys) {
        $newImport = $importMappings[$oldImport]
        $content = $content -replace [regex]::Escape("'$oldImport'"), "'$newImport'"
        $content = $content -replace [regex]::Escape("`"$oldImport`""), "`"$newImport`""
    }
    
    # Fix component usage in code
    foreach ($oldComponent in $componentMappings.Keys) {
        $newComponent = $componentMappings[$oldComponent]
        $content = $content -replace "\b$oldComponent\b", $newComponent
    }
    
    # Only write if content changed
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  ✓ Fixed imports in: $($file.Name)"
        $totalFixed++
    } else {
        Write-Host "  - No changes needed: $($file.Name)"
    }
}

Write-Host ""
Write-Host "Import fixing complete! Fixed $totalFixed files."
