'use client';

import { Fragment } from 'react';
import {
  RiCheckLine,
  RiInformationLine,
  RiSubtractLine,
} from '@remixicon/react';

import { cx } from '@/lib/utils';

import { Tooltip } from '@/components/ui/tooltip';

interface Plan {
  name: string;
  price: string;
  description: string;
  capacity: string[];
  features: string[];
  isStarter: boolean;
  isRecommended: boolean;
  buttonText: string;
  buttonLink: string;
}

const plans: Plan[] = [
  {
    name: 'Starter',
    price: '$0',
    description:
      'For individuals and freelancers that need a scalable database.',
    capacity: ['Up to 5 users, 1 admin', '1 workspace'],
    features: [
      'Up to 1000/req. per day',
      '5 GB max storage',
      'Community Slack Support',
    ],
    isStarter: true,
    isRecommended: false,
    buttonText: 'Get started',
    buttonLink: '#',
  },
  {
    name: 'Teams',
    price: '$49',
    description: 'For small teams and start-ups that need a scalable database.',
    capacity: ['Up to 100 users, 3 admins', 'Up to 20 workspaces'],
    features: [
      'Unlimited requests',
      '$0.07 per processed GB',
      '$0.34 per stored GB',
      'Slack Connect',
    ],
    isStarter: false,
    isRecommended: false,
    buttonText: 'Start 14-day trial',
    buttonLink: '#',
  },
  {
    name: 'Business',
    price: '$99',
    description:
      'For larger teams that need more advanced controls and features.',
    capacity: ['Up to 500 users, 10 admins', 'Unlimited workspaces'],
    features: [
      'Unlimited requests',
      'Volume discount',
      '$0.03 per processed GB',
      '$0.1 per stored GB',
      'Single Sign-On (SSO)',
    ],
    isStarter: false,
    isRecommended: true,
    buttonText: 'Start 14-day trial',
    buttonLink: '#',
  },
];

interface Feature {
  name: string;
  plans: Record<string, boolean | string>;
  tooltip?: string;
}

interface Section {
  name: string;
  features: Feature[];
}

const sections: Section[] = [
  {
    name: 'Workspace Features',
    features: [
      {
        name: 'Email notifications & webhooks',
        tooltip:
          'Consectetur qui culpa ipsum in ea irure duis culpa incididunt.',
        plans: { Starter: true, Teams: true, Business: true },
      },
      {
        name: 'Workspaces',
        tooltip:
          'Consectetur qui culpa ipsum in ea irure duis culpa incididunt.',
        plans: { Starter: '5', Teams: '10', Business: 'Unlimited' },
      },
      {
        name: 'Storage',
        tooltip:
          'Consectetur qui culpa ipsum in ea irure duis culpa incididunt.',
        plans: {
          Starter: '$0.65 per stored GB',
          Teams: '$0.34 per stored GB',
          Business: 'Customized¹',
        },
      },
      {
        name: 'Seats',
        tooltip:
          'Consectetur qui culpa ipsum in ea irure duis culpa incididunt.',
        plans: {
          Starter: '5 users',
          Teams: 'Up to 20 users',
          Business: 'Unlimited',
        },
      },
    ],
  },
  {
    name: 'Automation',
    features: [
      {
        name: 'Service accounts',
        tooltip:
          'Consectetur qui culpa ipsum in ea irure duis culpa incididunt.',
        plans: { Starter: true, Teams: true, Business: true },
      },
      {
        name: 'Admin API',
        tooltip:
          'Consectetur qui culpa ipsum in ea irure duis culpa incididunt.',
        plans: { Teams: true, Business: true },
      },
      {
        name: 'No-Code workflow builder',
        tooltip:
          'Consectetur qui culpa ipsum in ea irure duis culpa incididunt.',
        plans: { Starter: 'Limited', Teams: 'Standard', Business: 'Enhanced' },
      },
    ],
  },
  {
    name: 'Analytics',
    features: [
      {
        name: 'Analytics retention',
        tooltip:
          'Consectetur qui culpa ipsum in ea irure duis culpa incididunt.',
        plans: { Starter: '7 days', Teams: '1 year', Business: 'Unlimited' },
      },
      {
        name: 'Anomaly detection',
        tooltip:
          'Consectetur qui culpa ipsum in ea irure duis culpa incididunt.',
        plans: { Teams: true, Business: true },
      },
      {
        name: 'Custom report builder',
        tooltip:
          'Consectetur qui culpa ipsum in ea irure duis culpa incididunt.',
        plans: { Business: true },
      },
    ],
  },
  {
    name: 'Support',
    features: [
      {
        name: 'Slack',
        plans: {
          Starter: 'Community',
          Teams: 'Connect',
          Business: 'Dedicated agent',
        },
      },
      {
        name: 'Email',
        plans: { Starter: '2-4 days', Teams: '1-2 days', Business: 'Priority' },
      },
    ],
  },
];

export default function Example() {
  return (
    <div className="obfuscate">
      <section className="mx-auto py-10">
        <div className="hidden lg:block">
          <div className="relative">
            <table className="w-full table-fixed border-separate border-spacing-0 text-left">
              <caption className="sr-only">Pricing plan comparison</caption>
              <colgroup>
                <col className="w-2/5" />
                <col className="w-1/5" />
                <col className="w-1/5" />
                <col className="w-1/5" />
              </colgroup>
              <thead className="">
                <tr>
                  <th
                    scope="col"
                    className="border-b border-gray-100 bg-white pb-8 dark:border-gray-800 dark:bg-gray-950"
                  >
                    <div className="font-semibold leading-7 text-gray-900 dark:text-gray-50">
                      Compare prices
                    </div>
                    <div className="text-sm font-normal text-gray-600 dark:text-gray-400">
                      Price per month (billed yearly)
                    </div>
                  </th>
                  {plans.map((plan) => (
                    <th
                      key={plan.name}
                      scope="col"
                      className="border-b border-gray-100 bg-white px-6 pb-8 dark:border-gray-800 dark:bg-gray-950 lg:px-8"
                    >
                      <div
                        className={cx(
                          !plan.isStarter
                            ? 'text-blue-600 dark:text-blue-400'
                            : 'text-gray-900 dark:text-gray-50',
                          'font-semibold leading-7',
                        )}
                      >
                        {plan.name}
                      </div>
                      <div className="text-sm font-normal text-gray-600 dark:text-gray-400">
                        {plan.price}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {sections.map((section, sectionIdx) => (
                  <Fragment key={section.name}>
                    <tr>
                      <th
                        scope="colgroup"
                        colSpan={4}
                        className={cx(
                          sectionIdx === 0 ? 'pt-14' : 'pt-10',
                          'border-b border-gray-100 pb-4 text-base font-semibold leading-6 text-gray-900 dark:border-gray-800 dark:text-gray-50',
                        )}
                      >
                        {section.name}
                      </th>
                    </tr>
                    {section.features.map((feature) => (
                      <tr
                        key={feature.name}
                        className="transition hover:bg-blue-50/30 dark:hover:bg-blue-800/5"
                      >
                        <th
                          scope="row"
                          className="flex items-center gap-2 border-b border-gray-100 py-4 text-sm font-normal leading-6 text-gray-900 dark:border-gray-800 dark:text-gray-50"
                        >
                          <span>{feature.name}</span>
                          {feature.tooltip ? (
                            <Tooltip side="right" content={feature.tooltip}>
                              <RiInformationLine
                                className="size-4 shrink-0 text-gray-700 dark:text-gray-400"
                                aria-hidden="true"
                              />
                            </Tooltip>
                          ) : null}
                        </th>
                        {plans.map((plan) => (
                          <td
                            key={plan.name}
                            className="border-b border-gray-100 px-6 py-4 dark:border-gray-800 lg:px-8"
                          >
                            {typeof feature.plans[plan.name] === 'string' ? (
                              <div className="text-sm leading-6 text-gray-600 dark:text-gray-400">
                                {feature.plans[plan.name]}
                              </div>
                            ) : (
                              <>
                                {feature.plans[plan.name] === true ? (
                                  <RiCheckLine
                                    className="h-5 w-5 text-blue-600 dark:text-blue-400"
                                    aria-hidden="true"
                                  />
                                ) : (
                                  <RiSubtractLine
                                    className="h-5 w-5 text-gray-400 dark:text-gray-600"
                                    aria-hidden="true"
                                  />
                                )}

                                <span className="sr-only">
                                  {feature.plans[plan.name] === true
                                    ? 'Included'
                                    : 'Not included'}{' '}
                                  in {plan.name}
                                </span>
                              </>
                            )}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </Fragment>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
  );
}
