'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableFoot,
  TableHead,
  TableHeaderCell,
  TableRoot,
  TableRow,
} from '@/components/ui/table';

const data = [
  //array-start
  {
    workspace: 'sales_by_day_api',
    owner: '<PERSON>',
    status: 'Live',
    costs: '$3,509.00',
    region: 'US-West 1',
    capacity: '31.1%',
    lastEdited: '23/09/2023 13:00',
  },
  {
    workspace: 'marketing_campaign',
    owner: '<PERSON>',
    status: 'Live',
    costs: '$5,720.00',
    region: 'US-East 2',
    capacity: '81.3%',
    lastEdited: '22/09/2023 10:45',
  },
  {
    workspace: 'test_environment',
    owner: '<PERSON>',
    status: 'Inactive',
    costs: '$800.00',
    region: 'EU-Central 1',
    capacity: '40.8%',
    lastEdited: '25/09/2023 16:20',
  },
  {
    workspace: 'sales_campaign',
    owner: '<PERSON>',
    status: 'Live',
    costs: '$5,720.00',
    region: 'US-East 2',
    capacity: '51.4%',
    lastEdited: '22/09/2023 10:45',
  },
  {
    workspace: 'development_env',
    owner: '<PERSON>',
    status: 'Inactive',
    costs: '$4,200.00',
    region: 'EU-West 1',
    capacity: '60.4%',
    lastEdited: '21/09/2023 14:30',
  },
  {
    workspace: 'new_workspace_1',
    owner: 'Alice Brown',
    status: 'Inactive',
    costs: '$2,100.00',
    region: 'US-West 2',
    capacity: '75.9%',
    lastEdited: '24/09/2023 09:15',
  },
  //array-end
];

export default function Example() {
  return (
    <div className="obfuscate">
      <div className="sm:flex sm:items-center sm:justify-between sm:space-x-10">
        <div>
          <h3 className="font-semibold text-gray-900 dark:text-gray-50">
            Workspaces
          </h3>
          <p className="mt-1 text-sm/6 text-gray-500 dark:text-gray-500">
            Overview of all registered workspaces within your organization.
          </p>
        </div>
        <Button className="mt-4 w-full sm:mt-0 sm:w-fit">Add workspace</Button>
      </div>
      <TableRoot className="mt-8">
        <Table className="border-transparent dark:border-transparent">
          <TableHead>
            <TableRow>
              <TableHeaderCell>Workspace</TableHeaderCell>
              <TableHeaderCell>Owner</TableHeaderCell>
              <TableHeaderCell>Status</TableHeaderCell>
              <TableHeaderCell>Region</TableHeaderCell>
              <TableHeaderCell>Capacity</TableHeaderCell>
              <TableHeaderCell className="text-right">
                Last edited
              </TableHeaderCell>
              <TableHeaderCell className="text-right">Costs</TableHeaderCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data.map((item) => (
              <TableRow key={item.workspace}>
                <TableCell className="font-medium text-gray-900 dark:text-gray-50">
                  {item.workspace}
                </TableCell>
                <TableCell>{item.owner}</TableCell>
                <TableCell>{item.status}</TableCell>
                <TableCell>{item.region}</TableCell>
                <TableCell>{item.capacity}</TableCell>
                <TableCell className="text-right">{item.lastEdited}</TableCell>
                <TableCell className="text-right">{item.costs}</TableCell>
              </TableRow>
            ))}
          </TableBody>
          <TableFoot>
            <TableRow>
              <TableHeaderCell
                scope="row"
                colSpan={6}
                className="border-transparent text-right !font-normal !text-gray-600 dark:border-transparent dark:!text-gray-400"
              >
                Subtotal
              </TableHeaderCell>
              <TableCell className="text-right !font-normal">
                $22,049.00
              </TableCell>
            </TableRow>
            <TableRow>
              <TableHeaderCell
                scope="row"
                colSpan={6}
                className="border-transparent text-right !font-normal !text-gray-600 dark:border-transparent dark:!text-gray-400"
              >
                VAT (7.7%)
              </TableHeaderCell>
              <TableCell className="text-right !font-normal">
                $1,697.77
              </TableCell>
            </TableRow>
            <TableRow>
              <TableHeaderCell
                scope="row"
                colSpan={6}
                className="border-transparent text-right font-semibold dark:border-transparent"
              >
                Total
              </TableHeaderCell>
              <TableCell className="text-right font-semibold text-gray-900 dark:text-gray-50">
                $23,746.77
              </TableCell>
            </TableRow>
          </TableFoot>
        </Table>
      </TableRoot>
    </div>
  );
}
