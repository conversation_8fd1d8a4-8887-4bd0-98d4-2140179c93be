'use client';

import React from 'react';
import { RiArrowDownSLine } from '@remixicon/react';

import { BarChart, TooltipProps } from '@/components/BarChart';
import { Button } from '@/components/Button';
import { Divider } from '@/components/Divider';

const data = [
  //array-start
  {
    date: 'Jan 23',
    Running: 167,
  },
  {
    date: 'Feb 23',
    Running: 125,
  },
  {
    date: 'Mar 23',
    Running: 156,
  },
  {
    date: 'Apr 23',
    Running: 165,
  },
  {
    date: 'May 23',
    Running: 153,
  },
  {
    date: 'Jun 23',
    Running: 124,
  },
  {
    date: 'Jul 23',
    Running: 164,
  },
  {
    date: 'Aug 23',
    Running: 123,
  },
  {
    date: 'Sep 23',
    Running: 132,
  },
  {
    date: 'Oct 23',
    Running: 121,
  },
  {
    date: 'Nov 23',
    Running: 129,
  },
  {
    date: 'Dec 23',
    Running: 141,
  },
  //array-end
];

const valueFormatter = (number: number) => {
  return Intl.NumberFormat('us').format(number).toString() + ' bpm';
};

const CustomTooltip = ({ payload, active, label }: TooltipProps) => {
  if (!active || !payload || payload.length === 0) return null;
  const categoryPayload = payload[0];
  return (
    <div className="w-56 rounded-md border border-gray-200 bg-white p-2 text-sm shadow-md dark:border-gray-800 dark:bg-gray-950">
      <div className="flex space-x-2.5">
        <span
          className={`w-1 bg-${categoryPayload.color}-500 rounded`}
          aria-hidden={true}
        />
        <div className="w-full truncate">
          <p className="font-medium text-gray-900 dark:text-gray-50">{label}</p>
          <p className="flex items-center justify-between space-x-8">
            <span className="truncate text-gray-500 dark:text-gray-500">
              {categoryPayload.category}
            </span>
            <span className="font-medium text-gray-900 dark:text-gray-50">
              {valueFormatter(categoryPayload.value)}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default function Example() {
  const [showDemo, setShowDemo] = React.useState(false);
  return (
    <div className="obfuscate">
      <div className="flex w-full justify-center">
        <CustomTooltip
          label="Jan 23"
          active={true}
          payload={[
            {
              category: 'Running',
              value: 145,
              index: 'Jan 23',
              color: 'blue',
              payload: {
                date: 'Jan 23',
                Running: 145,
              },
            },
          ]}
        />
      </div>
      <Divider className="mt-12">
        <Button
          variant="light"
          onClick={() => setShowDemo(!showDemo)}
          className="group !rounded-full !bg-gray-100 !text-gray-500 hover:!bg-gray-100 dark:!bg-gray-900 dark:!text-gray-500 hover:dark:!bg-gray-900"
          tabIndex={0}
        >
          <RiArrowDownSLine
            aria-hidden={true}
            className={`-ml-1 size-5 transition-all group-hover:text-gray-900 group-hover:dark:text-gray-50 ${showDemo ? 'rotate-180' : ''} `}
          />
          <span className="ml-1 transition-all group-hover:text-gray-900 group-hover:dark:text-gray-50">
            {showDemo ? 'Hide Demo' : 'Show Demo'}
          </span>
        </Button>
      </Divider>
      {showDemo ? (
        <>
          <BarChart
            className="mt-12 hidden !h-72 sm:block"
            data={data}
            index="date"
            categories={['Running']}
            valueFormatter={valueFormatter}
            yAxisWidth={70}
            showLegend={false}
            customTooltip={CustomTooltip}
          />
          <BarChart
            className="mt-12 !h-72 sm:hidden"
            data={data}
            index="date"
            categories={['Running']}
            valueFormatter={valueFormatter}
            showYAxis={false}
            showLegend={false}
            startEndOnly={true}
            customTooltip={CustomTooltip}
          />
        </>
      ) : null}
    </div>
  );
}
