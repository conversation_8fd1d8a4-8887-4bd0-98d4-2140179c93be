const fs = require('fs');
const path = require('path');

// Component mapping from incorrect to correct paths
const importMappings = {
  "'@/components/Accordion'": "'@/components/ui/accordion'",
  "'@/components/Alert'": "'@/components/ui/alert'",
  "'@/components/AlertDialog'": "'@/components/ui/alert-dialog'",
  "'@/components/AreaChart'": "'@/components/ui/chart'",
  "'@/components/Avatar'": "'@/components/ui/avatar'",
  "'@/components/Badge'": "'@/components/ui/badge'",
  "'@/components/BarChart'": "'@/components/ui/chart'",
  "'@/components/Button'": "'@/components/ui/button'",
  "'@/components/Calendar'": "'@/components/ui/calendar'",
  "'@/components/Card'": "'@/components/ui/card'",
  "'@/components/CategoryBar'": "'@/components/ui/chart'",
  "'@/components/Chart'": "'@/components/ui/chart'",
  "'@/components/Checkbox'": "'@/components/ui/checkbox'",
  "'@/components/ComboChart'": "'@/components/ui/chart'",
  "'@/components/DatePicker'": "'@/components/ui/calendar'",
  "'@/components/Dialog'": "'@/components/ui/dialog'",
  "'@/components/Divider'": "'@/components/ui/separator'",
  "'@/components/DonutChart'": "'@/components/ui/chart'",
  "'@/components/Drawer'": "'@/components/ui/drawer'",
  "'@/components/DropdownMenu'": "'@/components/ui/dropdown-menu'",
  "'@/components/Input'": "'@/components/ui/input'",
  "'@/components/Label'": "'@/components/ui/label'",
  "'@/components/LineChart'": "'@/components/ui/chart'",
  "'@/components/PieChart'": "'@/components/ui/chart'",
  "'@/components/Progress'": "'@/components/ui/progress'",
  "'@/components/ProgressBar'": "'@/components/ui/progress'",
  "'@/components/Select'": "'@/components/ui/select'",
  "'@/components/Separator'": "'@/components/ui/separator'",
  "'@/components/Switch'": "'@/components/ui/switch'",
  "'@/components/Table'": "'@/components/ui/table'",
  "'@/components/Tabs'": "'@/components/ui/tabs'",
  "'@/components/Tooltip'": "'@/components/ui/tooltip'",
  "'@/components/Tracker'": "'@/components/ui/chart'"
};

// Component name mappings for usage in code
const componentMappings = {
  "Divider": "Separator",
  "ProgressBar": "Progress"
};

function fixImportsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // Fix import statements
    for (const [oldImport, newImport] of Object.entries(importMappings)) {
      content = content.replace(new RegExp(oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newImport);
    }
    
    // Fix component usage in code
    for (const [oldComponent, newComponent] of Object.entries(componentMappings)) {
      content = content.replace(new RegExp(`\\b${oldComponent}\\b`, 'g'), newComponent);
    }
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content);
      console.log(`✓ Fixed imports in: ${path.basename(filePath)}`);
      return true;
    } else {
      console.log(`- No changes needed: ${path.basename(filePath)}`);
      return false;
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

function findTsxFiles(dir) {
  const files = [];
  try {
    const items = fs.readdirSync(dir);
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      if (stat.isDirectory()) {
        files.push(...findTsxFiles(fullPath));
      } else if (item.endsWith('.tsx')) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  return files;
}

// Main execution
console.log('Starting comprehensive import fix...');

const blocksDir = path.join('src', 'app', 'demo', '[name]', 'blocks');
console.log(`Looking for files in: ${blocksDir}`);

const files = findTsxFiles(blocksDir);
console.log(`Found ${files.length} files to process...`);

if (files.length === 0) {
  console.log('No files found. Checking if directory exists...');
  console.log('Directory exists:', fs.existsSync(blocksDir));
  process.exit(1);
}

let totalFixed = 0;
for (const file of files) {
  if (fixImportsInFile(file)) {
    totalFixed++;
  }
}

console.log(`\nImport fixing complete! Fixed ${totalFixed} files.`);
