{"$schema": "https://ui.shadcn.com/schema/registry.json", "name": "Registry Starter", "homepage": "https://registry-starter.vercel.app", "items": [{"name": "registry", "type": "registry:style", "cssVars": {"light": {"primary": "oklch(0.52 0.13 144.17)", "primary-foreground": "oklch(1.0 0 0)", "radius": "0.5rem"}, "dark": {"primary": "oklch(0.52 0.13 144.17)", "primary-foreground": "oklch(1.0 0 0)"}}, "files": []}, {"name": "theme", "type": "registry:theme", "title": "Global Theme", "description": "Brand themed styles using Tailwind and Shadcn/ui", "files": [{"path": "src/app/tokens.css", "type": "registry:file", "target": "app/tokens.css"}, {"path": "src/v0/globals.css", "type": "registry:file", "target": "app/globals.css"}, {"path": "src/v0/tailwind.config.ts", "type": "registry:file", "target": "tailwind.config.ts"}]}, {"name": "blank", "type": "registry:block", "title": "Blank", "description": "A blank application with all brand components and code", "registryDependencies": ["https://registry-starter.vercel.app/r/brand-header.json", "https://registry-starter.vercel.app/r/brand-sidebar.json", "https://registry-starter.vercel.app/r/product-grid.json", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dashboard", "type": "registry:block", "title": "Dashboard", "description": "A dashboard application with your brand themed components", "registryDependencies": ["https://registry-starter.vercel.app/r/sonner.json", "https://registry-starter.vercel.app/r/brand-header.json", "https://registry-starter.vercel.app/r/brand-sidebar.json", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/shell-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/dashboard-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "store", "type": "registry:block", "title": "Store", "description": "A store application with your brand themed components", "registryDependencies": ["https://registry-starter.vercel.app/r/product-grid.json", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/store-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "account-and-user-management-01", "type": "registry:block", "title": "Account and User Management 01", "description": "Account and user management interface", "files": [{"path": "src/app/demo/[name]/blocks/account-and-user-management-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "account-and-user-management-02", "type": "registry:block", "title": "Account and User Management 02", "description": "Account and user management interface", "files": [{"path": "src/app/demo/[name]/blocks/account-and-user-management-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "account-and-user-management-03", "type": "registry:block", "title": "Account and User Management 03", "description": "Account and user management interface", "files": [{"path": "src/app/demo/[name]/blocks/account-and-user-management-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "account-and-user-management-04", "type": "registry:block", "title": "Account and User Management 04", "description": "Account and user management interface", "files": [{"path": "src/app/demo/[name]/blocks/account-and-user-management-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "account-and-user-management-05", "type": "registry:block", "title": "Account and User Management 05", "description": "Account and user management interface", "files": [{"path": "src/app/demo/[name]/blocks/account-and-user-management-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "account-and-user-management-06", "type": "registry:block", "title": "Account and User Management 06", "description": "Account and user management interface", "files": [{"path": "src/app/demo/[name]/blocks/account-and-user-management-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "account-and-user-management-07", "type": "registry:block", "title": "Account and User Management 07", "description": "Account and user management interface", "files": [{"path": "src/app/demo/[name]/blocks/account-and-user-management-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "account-and-user-management-08", "type": "registry:block", "title": "Account and User Management 08", "description": "Account and user management interface", "files": [{"path": "src/app/demo/[name]/blocks/account-and-user-management-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "account-and-user-management-09", "type": "registry:block", "title": "Account and User Management 09", "description": "Account and user management interface", "files": [{"path": "src/app/demo/[name]/blocks/account-and-user-management-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-01", "type": "registry:block", "title": "Area Chart 01", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-02", "type": "registry:block", "title": "Area Chart 02", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-03", "type": "registry:block", "title": "Area Chart 03", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-04", "type": "registry:block", "title": "Area Chart 04", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-05", "type": "registry:block", "title": "Area Chart 05", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-06", "type": "registry:block", "title": "Area Chart 06", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-07", "type": "registry:block", "title": "Area Chart 07", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-08", "type": "registry:block", "title": "Area Chart 08", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-09", "type": "registry:block", "title": "Area Chart 09", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-10", "type": "registry:block", "title": "Area Chart 10", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-11", "type": "registry:block", "title": "Area Chart 11", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-11.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-12", "type": "registry:block", "title": "Area Chart 12", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-12.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-13", "type": "registry:block", "title": "Area Chart 13", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-13.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-14", "type": "registry:block", "title": "Area Chart 14", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-14.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-15", "type": "registry:block", "title": "Area Chart 15", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-15.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "area-chart-16", "type": "registry:block", "title": "Area Chart 16", "description": "Area chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/area-chart-16.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "banner-01", "type": "registry:block", "title": "Banner 01", "description": "Banner component for notifications and announcements", "files": [{"path": "src/app/demo/[name]/blocks/banner-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "banner-02", "type": "registry:block", "title": "Banner 02", "description": "Banner component for notifications and announcements", "files": [{"path": "src/app/demo/[name]/blocks/banner-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "banner-03", "type": "registry:block", "title": "Banner 03", "description": "Banner component for notifications and announcements", "files": [{"path": "src/app/demo/[name]/blocks/banner-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "banner-04", "type": "registry:block", "title": "Banner 04", "description": "Banner component for notifications and announcements", "files": [{"path": "src/app/demo/[name]/blocks/banner-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "banner-05", "type": "registry:block", "title": "Banner 05", "description": "Banner component for notifications and announcements", "files": [{"path": "src/app/demo/[name]/blocks/banner-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-chart-01", "type": "registry:block", "title": "Bar Chart 01", "description": "Bar chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-chart-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-chart-02", "type": "registry:block", "title": "Bar Chart 02", "description": "Bar chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-chart-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-chart-03", "type": "registry:block", "title": "Bar Chart 03", "description": "Bar chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-chart-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-chart-04", "type": "registry:block", "title": "Bar Chart 04", "description": "Bar chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-chart-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-chart-05", "type": "registry:block", "title": "Bar Chart 05", "description": "Bar chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-chart-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-chart-06", "type": "registry:block", "title": "Bar Chart 06", "description": "Bar chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-chart-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-chart-07", "type": "registry:block", "title": "Bar Chart 07", "description": "Bar chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-chart-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-chart-08", "type": "registry:block", "title": "Bar Chart 08", "description": "Bar chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-chart-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-chart-09", "type": "registry:block", "title": "Bar Chart 09", "description": "Bar chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-chart-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-chart-10", "type": "registry:block", "title": "Bar Chart 10", "description": "Bar chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-chart-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-chart-11", "type": "registry:block", "title": "Bar Chart 11", "description": "Bar chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-chart-11.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-chart-12", "type": "registry:block", "title": "Bar Chart 12", "description": "Bar chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-chart-12.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-list-01", "type": "registry:block", "title": "Bar List 01", "description": "Bar list visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-list-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-list-02", "type": "registry:block", "title": "Bar List 02", "description": "Bar list visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-list-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-list-03", "type": "registry:block", "title": "Bar List 03", "description": "Bar list visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-list-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-list-04", "type": "registry:block", "title": "Bar List 04", "description": "Bar list visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-list-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-list-05", "type": "registry:block", "title": "Bar List 05", "description": "Bar list visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-list-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-list-06", "type": "registry:block", "title": "Bar List 06", "description": "Bar list visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-list-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "bar-list-07", "type": "registry:block", "title": "Bar List 07", "description": "Bar list visualization component", "files": [{"path": "src/app/demo/[name]/blocks/bar-list-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "billing-usage-01", "type": "registry:block", "title": "Billing Usage 01", "description": "Billing and usage tracking component", "files": [{"path": "src/app/demo/[name]/blocks/billing-usage-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "billing-usage-02", "type": "registry:block", "title": "Billing Usage 02", "description": "Billing and usage tracking component", "files": [{"path": "src/app/demo/[name]/blocks/billing-usage-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "billing-usage-03", "type": "registry:block", "title": "Billing Usage 03", "description": "Billing and usage tracking component", "files": [{"path": "src/app/demo/[name]/blocks/billing-usage-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "billing-usage-04", "type": "registry:block", "title": "Billing Usage 04", "description": "Billing and usage tracking component", "files": [{"path": "src/app/demo/[name]/blocks/billing-usage-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "billing-usage-05", "type": "registry:block", "title": "Billing Usage 05", "description": "Billing and usage tracking component", "files": [{"path": "src/app/demo/[name]/blocks/billing-usage-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "billing-usage-06", "type": "registry:block", "title": "Billing Usage 06", "description": "Billing and usage tracking component", "files": [{"path": "src/app/demo/[name]/blocks/billing-usage-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "billing-usage-07", "type": "registry:block", "title": "Billing Usage 07", "description": "Billing and usage tracking component", "files": [{"path": "src/app/demo/[name]/blocks/billing-usage-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "billing-usage-08", "type": "registry:block", "title": "Billing Usage 08", "description": "Billing and usage tracking component", "files": [{"path": "src/app/demo/[name]/blocks/billing-usage-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "billing-usage-09", "type": "registry:block", "title": "Billing Usage 09", "description": "Billing and usage tracking component", "files": [{"path": "src/app/demo/[name]/blocks/billing-usage-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "billing-usage-10", "type": "registry:block", "title": "Billing Usage 10", "description": "Billing and usage tracking component", "files": [{"path": "src/app/demo/[name]/blocks/billing-usage-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-01", "type": "registry:block", "title": "Chart Composition 01", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-02", "type": "registry:block", "title": "Chart Composition 02", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-03", "type": "registry:block", "title": "Chart Composition 03", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-04", "type": "registry:block", "title": "Chart Composition 04", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-05", "type": "registry:block", "title": "Chart Composition 05", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-06", "type": "registry:block", "title": "Chart Composition 06", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-07", "type": "registry:block", "title": "Chart Composition 07", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-08", "type": "registry:block", "title": "Chart Composition 08", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-09", "type": "registry:block", "title": "Chart Composition 09", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-10", "type": "registry:block", "title": "Chart Composition 10", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-11", "type": "registry:block", "title": "Chart Composition 11", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-11.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-12", "type": "registry:block", "title": "Chart Composition 12", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-12.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-13", "type": "registry:block", "title": "Chart Composition 13", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-13.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-14", "type": "registry:block", "title": "Chart Composition 14", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-14.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-composition-15", "type": "registry:block", "title": "Chart Composition 15", "description": "Chart composition visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-composition-15.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "login-01", "type": "registry:block", "title": "Login 01", "description": "Login form component", "files": [{"path": "src/app/demo/[name]/blocks/login-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "login-02", "type": "registry:block", "title": "Login 02", "description": "Login form component", "files": [{"path": "src/app/demo/[name]/blocks/login-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "login-03", "type": "registry:block", "title": "Login 03", "description": "Login form component", "files": [{"path": "src/app/demo/[name]/blocks/login-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "login-04", "type": "registry:block", "title": "Login 04", "description": "Login form component", "files": [{"path": "src/app/demo/[name]/blocks/login-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "login-05", "type": "registry:block", "title": "Login 05", "description": "Login form component", "files": [{"path": "src/app/demo/[name]/blocks/login-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "login-06", "type": "registry:block", "title": "Login 06", "description": "Login form component", "files": [{"path": "src/app/demo/[name]/blocks/login-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "login-07", "type": "registry:block", "title": "Login 07", "description": "Login form component", "files": [{"path": "src/app/demo/[name]/blocks/login-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "login-08", "type": "registry:block", "title": "Login 08", "description": "Login form component", "files": [{"path": "src/app/demo/[name]/blocks/login-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "login-09", "type": "registry:block", "title": "Login 09", "description": "Login form component", "files": [{"path": "src/app/demo/[name]/blocks/login-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "login-10", "type": "registry:block", "title": "Login 10", "description": "Login form component", "files": [{"path": "src/app/demo/[name]/blocks/login-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "workspace-usage", "type": "registry:block", "title": "Workspace Usage", "description": "Workspace usage tracking component", "files": [{"path": "src/app/demo/[name]/blocks/workspace-usage.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-01", "type": "registry:block", "title": "Chart Tooltip 01", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-02", "type": "registry:block", "title": "Chart Tooltip 02", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-03", "type": "registry:block", "title": "Chart Tooltip 03", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-04", "type": "registry:block", "title": "Chart Tooltip 04", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-05", "type": "registry:block", "title": "Chart Tooltip 05", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-06", "type": "registry:block", "title": "Chart Tooltip 06", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-07", "type": "registry:block", "title": "Chart Tooltip 07", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-08", "type": "registry:block", "title": "Chart Tooltip 08", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-09", "type": "registry:block", "title": "Chart Tooltip 09", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-10", "type": "registry:block", "title": "Chart Tooltip 10", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-11", "type": "registry:block", "title": "Chart Tooltip 11", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-11.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-12", "type": "registry:block", "title": "Chart Tooltip 12", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-12.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-13", "type": "registry:block", "title": "Chart Tooltip 13", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-13.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-14", "type": "registry:block", "title": "Chart Tooltip 14", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-14.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-15", "type": "registry:block", "title": "Chart Tooltip 15", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-15.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-16", "type": "registry:block", "title": "Chart Tooltip 16", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-16.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-17", "type": "registry:block", "title": "Chart Tooltip 17", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-17.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-18", "type": "registry:block", "title": "Chart Tooltip 18", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-18.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-19", "type": "registry:block", "title": "Chart Tooltip 19", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-19.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-20", "type": "registry:block", "title": "Chart Tooltip 20", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-20.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "chart-tooltip-21", "type": "registry:block", "title": "Chart Tooltip 21", "description": "Chart tooltip visualization component", "files": [{"path": "src/app/demo/[name]/blocks/chart-tooltip-21.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dialog-01", "type": "registry:block", "title": "Dialog 01", "description": "Dialog component for modals and overlays", "files": [{"path": "src/app/demo/[name]/blocks/dialog-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dialog-02", "type": "registry:block", "title": "Dialog 02", "description": "Dialog component for modals and overlays", "files": [{"path": "src/app/demo/[name]/blocks/dialog-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dialog-03", "type": "registry:block", "title": "Dialog 03", "description": "Dialog component for modals and overlays", "files": [{"path": "src/app/demo/[name]/blocks/dialog-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dialog-04", "type": "registry:block", "title": "Dialog 04", "description": "Dialog component for modals and overlays", "files": [{"path": "src/app/demo/[name]/blocks/dialog-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dialog-05", "type": "registry:block", "title": "Dialog 05", "description": "Dialog component for modals and overlays", "files": [{"path": "src/app/demo/[name]/blocks/dialog-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dialog-06", "type": "registry:block", "title": "Dialog 06", "description": "Dialog component for modals and overlays", "files": [{"path": "src/app/demo/[name]/blocks/dialog-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dialog-07", "type": "registry:block", "title": "Dialog 07", "description": "Dialog component for modals and overlays", "files": [{"path": "src/app/demo/[name]/blocks/dialog-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dialog-08", "type": "registry:block", "title": "Dialog 08", "description": "Dialog component for modals and overlays", "files": [{"path": "src/app/demo/[name]/blocks/dialog-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dialog-09", "type": "registry:block", "title": "Dialog 09", "description": "Dialog component for modals and overlays", "files": [{"path": "src/app/demo/[name]/blocks/dialog-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "donut-chart-01", "type": "registry:block", "title": "Donut Chart 01", "description": "Donut chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/donut-chart-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "donut-chart-02", "type": "registry:block", "title": "Donut Chart 02", "description": "Donut chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/donut-chart-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "donut-chart-03", "type": "registry:block", "title": "Donut Chart 03", "description": "Donut chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/donut-chart-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "donut-chart-04", "type": "registry:block", "title": "Donut Chart 04", "description": "Donut chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/donut-chart-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "donut-chart-05", "type": "registry:block", "title": "Donut Chart 05", "description": "Donut chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/donut-chart-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "donut-chart-06", "type": "registry:block", "title": "Donut Chart 06", "description": "Donut chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/donut-chart-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "donut-chart-07", "type": "registry:block", "title": "Donut Chart 07", "description": "Donut chart visualization component", "files": [{"path": "src/app/demo/[name]/blocks/donut-chart-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "empty-state-01", "type": "registry:block", "title": "Empty State 01", "description": "Empty state component for when no data is available", "files": [{"path": "src/app/demo/[name]/blocks/empty-state-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "empty-state-02", "type": "registry:block", "title": "Empty State 02", "description": "Empty state component for when no data is available", "files": [{"path": "src/app/demo/[name]/blocks/empty-state-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "empty-state-03", "type": "registry:block", "title": "Empty State 03", "description": "Empty state component for when no data is available", "files": [{"path": "src/app/demo/[name]/blocks/empty-state-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "empty-state-04", "type": "registry:block", "title": "Empty State 04", "description": "Empty state component for when no data is available", "files": [{"path": "src/app/demo/[name]/blocks/empty-state-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "empty-state-05", "type": "registry:block", "title": "Empty State 05", "description": "Empty state component for when no data is available", "files": [{"path": "src/app/demo/[name]/blocks/empty-state-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "empty-state-06", "type": "registry:block", "title": "Empty State 06", "description": "Empty state component for when no data is available", "files": [{"path": "src/app/demo/[name]/blocks/empty-state-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "empty-state-07", "type": "registry:block", "title": "Empty State 07", "description": "Empty state component for when no data is available", "files": [{"path": "src/app/demo/[name]/blocks/empty-state-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "empty-state-08", "type": "registry:block", "title": "Empty State 08", "description": "Empty state component for when no data is available", "files": [{"path": "src/app/demo/[name]/blocks/empty-state-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "empty-state-09", "type": "registry:block", "title": "Empty State 09", "description": "Empty state component for when no data is available", "files": [{"path": "src/app/demo/[name]/blocks/empty-state-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "empty-state-10", "type": "registry:block", "title": "Empty State 10", "description": "Empty state component for when no data is available", "files": [{"path": "src/app/demo/[name]/blocks/empty-state-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "feature-section-01", "type": "registry:block", "title": "Feature Section 01", "description": "Feature section component for showcasing product features", "files": [{"path": "src/app/demo/[name]/blocks/feature-section-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "feature-section-02", "type": "registry:block", "title": "Feature Section 02", "description": "Feature section component for showcasing product features", "files": [{"path": "src/app/demo/[name]/blocks/feature-section-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "feature-section-03", "type": "registry:block", "title": "Feature Section 03", "description": "Feature section component for showcasing product features", "files": [{"path": "src/app/demo/[name]/blocks/feature-section-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "feature-section-04", "type": "registry:block", "title": "Feature Section 04", "description": "Feature section component for showcasing product features", "files": [{"path": "src/app/demo/[name]/blocks/feature-section-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "feature-section-05", "type": "registry:block", "title": "Feature Section 05", "description": "Feature section component for showcasing product features", "files": [{"path": "src/app/demo/[name]/blocks/feature-section-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "feature-section-06", "type": "registry:block", "title": "Feature Section 06", "description": "Feature section component for showcasing product features", "files": [{"path": "src/app/demo/[name]/blocks/feature-section-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "feature-section-07", "type": "registry:block", "title": "Feature Section 07", "description": "Feature section component for showcasing product features", "files": [{"path": "src/app/demo/[name]/blocks/feature-section-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "feature-section-08", "type": "registry:block", "title": "Feature Section 08", "description": "Feature section component for showcasing product features", "files": [{"path": "src/app/demo/[name]/blocks/feature-section-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "feature-section-09", "type": "registry:block", "title": "Feature Section 09", "description": "Feature section component for showcasing product features", "files": [{"path": "src/app/demo/[name]/blocks/feature-section-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "feature-section-10", "type": "registry:block", "title": "Feature Section 10", "description": "Feature section component for showcasing product features", "files": [{"path": "src/app/demo/[name]/blocks/feature-section-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-01", "type": "registry:block", "title": "Grid List 01", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-02", "type": "registry:block", "title": "Grid List 02", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-03", "type": "registry:block", "title": "Grid List 03", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-04", "type": "registry:block", "title": "Grid List 04", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-05", "type": "registry:block", "title": "Grid List 05", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-06", "type": "registry:block", "title": "Grid List 06", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-07", "type": "registry:block", "title": "Grid List 07", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-08", "type": "registry:block", "title": "Grid List 08", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-09", "type": "registry:block", "title": "Grid List 09", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-10", "type": "registry:block", "title": "Grid List 10", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-11", "type": "registry:block", "title": "Grid List 11", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-11.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-12", "type": "registry:block", "title": "Grid List 12", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-12.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-13", "type": "registry:block", "title": "Grid List 13", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-13.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-14", "type": "registry:block", "title": "Grid List 14", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-14.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "grid-list-15", "type": "registry:block", "title": "Grid List 15", "description": "Grid list component for displaying items in a grid layout", "files": [{"path": "src/app/demo/[name]/blocks/grid-list-15.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-01", "type": "registry:block", "title": "Onboarding Feed 01", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-02", "type": "registry:block", "title": "Onboarding Feed 02", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-03", "type": "registry:block", "title": "Onboarding Feed 03", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-04", "type": "registry:block", "title": "Onboarding Feed 04", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-05", "type": "registry:block", "title": "Onboarding Feed 05", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-06", "type": "registry:block", "title": "Onboarding Feed 06", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-07", "type": "registry:block", "title": "Onboarding Feed 07", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-08", "type": "registry:block", "title": "Onboarding Feed 08", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-09", "type": "registry:block", "title": "Onboarding Feed 09", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-10", "type": "registry:block", "title": "Onboarding Feed 10", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-11", "type": "registry:block", "title": "Onboarding Feed 11", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-11.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-12", "type": "registry:block", "title": "Onboarding Feed 12", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-12.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-13", "type": "registry:block", "title": "Onboarding Feed 13", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-13.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-14", "type": "registry:block", "title": "Onboarding Feed 14", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-14.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-15", "type": "registry:block", "title": "Onboarding Feed 15", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-15.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "onboarding-feed-16", "type": "registry:block", "title": "Onboarding Feed 16", "description": "Onboarding feed component for user guidance", "files": [{"path": "src/app/demo/[name]/blocks/onboarding-feed-16.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-01", "type": "registry:block", "title": "Table 01", "description": "Table component for displaying tabular data", "files": [{"path": "src/app/demo/[name]/blocks/table-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-02", "type": "registry:block", "title": "Table 02", "description": "Table component for displaying tabular data", "files": [{"path": "src/app/demo/[name]/blocks/table-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-03", "type": "registry:block", "title": "Table 03", "description": "Table component for displaying tabular data", "files": [{"path": "src/app/demo/[name]/blocks/table-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-04", "type": "registry:block", "title": "Table 04", "description": "Table component for displaying tabular data", "files": [{"path": "src/app/demo/[name]/blocks/table-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-05", "type": "registry:block", "title": "Table 05", "description": "Table component for displaying tabular data", "files": [{"path": "src/app/demo/[name]/blocks/table-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-06", "type": "registry:block", "title": "Table 06", "description": "Table component for displaying tabular data", "files": [{"path": "src/app/demo/[name]/blocks/table-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-07", "type": "registry:block", "title": "Table 07", "description": "Table component for displaying tabular data", "files": [{"path": "src/app/demo/[name]/blocks/table-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-08", "type": "registry:block", "title": "Table 08", "description": "Table component for displaying tabular data", "files": [{"path": "src/app/demo/[name]/blocks/table-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-09", "type": "registry:block", "title": "Table 09", "description": "Table component for displaying tabular data", "files": [{"path": "src/app/demo/[name]/blocks/table-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-10", "type": "registry:block", "title": "Table 10", "description": "Table component for displaying tabular data", "files": [{"path": "src/app/demo/[name]/blocks/table-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-11", "type": "registry:block", "title": "Table 11", "description": "Table component for displaying tabular data", "files": [{"path": "src/app/demo/[name]/blocks/table-11.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-action-01", "type": "registry:block", "title": "Table Action 01", "description": "Table with action buttons and interactive elements", "files": [{"path": "src/app/demo/[name]/blocks/table-action-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-action-02", "type": "registry:block", "title": "Table Action 02", "description": "Table with action buttons and interactive elements", "files": [{"path": "src/app/demo/[name]/blocks/table-action-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-action-03", "type": "registry:block", "title": "Table Action 03", "description": "Table with action buttons and interactive elements", "files": [{"path": "src/app/demo/[name]/blocks/table-action-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-action-04", "type": "registry:block", "title": "Table Action 04", "description": "Table with action buttons and interactive elements", "files": [{"path": "src/app/demo/[name]/blocks/table-action-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-action-05", "type": "registry:block", "title": "Table Action 05", "description": "Table with action buttons and interactive elements", "files": [{"path": "src/app/demo/[name]/blocks/table-action-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-action-06", "type": "registry:block", "title": "Table Action 06", "description": "Table with action buttons and interactive elements", "files": [{"path": "src/app/demo/[name]/blocks/table-action-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-action-07", "type": "registry:block", "title": "Table Action 07", "description": "Table with action buttons and interactive elements", "files": [{"path": "src/app/demo/[name]/blocks/table-action-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-action-08", "type": "registry:block", "title": "Table Action 08", "description": "Table with action buttons and interactive elements", "files": [{"path": "src/app/demo/[name]/blocks/table-action-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-action-09", "type": "registry:block", "title": "Table Action 09", "description": "Table with action buttons and interactive elements", "files": [{"path": "src/app/demo/[name]/blocks/table-action-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-action-10", "type": "registry:block", "title": "Table Action 10", "description": "Table with action buttons and interactive elements", "files": [{"path": "src/app/demo/[name]/blocks/table-action-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "table-action-11", "type": "registry:block", "title": "Table Action 11", "description": "Table with action buttons and interactive elements", "files": [{"path": "src/app/demo/[name]/blocks/table-action-11.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "tracker-01", "type": "registry:block", "title": "Tracker 01", "description": "Tracker component for monitoring progress and metrics", "files": [{"path": "src/app/demo/[name]/blocks/tracker-01.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "tracker-02", "type": "registry:block", "title": "Tracker 02", "description": "Tracker component for monitoring progress and metrics", "files": [{"path": "src/app/demo/[name]/blocks/tracker-02.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "tracker-03", "type": "registry:block", "title": "Tracker 03", "description": "Tracker component for monitoring progress and metrics", "files": [{"path": "src/app/demo/[name]/blocks/tracker-03.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "tracker-04", "type": "registry:block", "title": "Tracker 04", "description": "Tracker component for monitoring progress and metrics", "files": [{"path": "src/app/demo/[name]/blocks/tracker-04.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "tracker-05", "type": "registry:block", "title": "Tracker 05", "description": "Tracker component for monitoring progress and metrics", "files": [{"path": "src/app/demo/[name]/blocks/tracker-05.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "tracker-06", "type": "registry:block", "title": "Tracker 06", "description": "Tracker component for monitoring progress and metrics", "files": [{"path": "src/app/demo/[name]/blocks/tracker-06.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "tracker-07", "type": "registry:block", "title": "Tracker 07", "description": "Tracker component for monitoring progress and metrics", "files": [{"path": "src/app/demo/[name]/blocks/tracker-07.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "tracker-08", "type": "registry:block", "title": "Tracker 08", "description": "Tracker component for monitoring progress and metrics", "files": [{"path": "src/app/demo/[name]/blocks/tracker-08.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "tracker-09", "type": "registry:block", "title": "Tracker 09", "description": "Tracker component for monitoring progress and metrics", "files": [{"path": "src/app/demo/[name]/blocks/tracker-09.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "tracker-10", "type": "registry:block", "title": "Tracker 10", "description": "Tracker component for monitoring progress and metrics", "files": [{"path": "src/app/demo/[name]/blocks/tracker-10.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "brand-header", "type": "registry:component", "title": "<PERSON>er", "description": "A styled, simple, reusable header", "registryDependencies": ["button", "input", "avatar", "sidebar", "https://registry-starter.vercel.app/r/sonner.json", "https://registry-starter.vercel.app/r/logo.json", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/brand-header.tsx", "type": "registry:component"}, {"path": "src/v0/shell-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "brand-sidebar", "type": "registry:component", "title": "Brand Sidebar", "description": "A styled, simple, reusable sidebar", "registryDependencies": ["badge", "button", "sidebar", "https://registry-starter.vercel.app/r/sonner.json", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/brand-sidebar.tsx", "type": "registry:component"}, {"path": "src/v0/shell-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "login", "type": "registry:component", "title": "<PERSON><PERSON>", "description": "Username & password login section with customer quote.", "registryDependencies": ["badge", "button", "https://registry-starter.vercel.app/r/theme.json", "https://registry-starter.vercel.app/r/logo.json"], "files": [{"path": "src/components/login.tsx", "type": "registry:component"}, {"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "logo", "type": "registry:component", "title": "Brand Logo", "description": "A styled, simple, reusable logo", "registryDependencies": ["https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/logo.tsx", "type": "registry:component"}, {"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "hero", "type": "registry:component", "title": "Hero", "description": "Attention-grabbing section for the top of your landing pages.", "registryDependencies": ["badge", "button", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/hero.tsx", "type": "registry:component"}, {"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "promo", "type": "registry:component", "title": "Promo", "description": "Attention-grabbing section to display the current promotional deal.", "registryDependencies": ["button", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/promo.tsx", "type": "registry:component"}, {"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "product-grid", "type": "registry:component", "title": "Product Grid", "description": "Product grid displaying all products with API to fetch data", "registryDependencies": ["https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/product-grid.tsx", "type": "registry:component"}, {"path": "src/lib/products.ts", "type": "registry:lib"}, {"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "accordion", "type": "registry:ui", "title": "Accordion", "description": "A vertically stacked set of interactive headings that each reveal a section of content.", "registryDependencies": ["accordion", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "alert", "type": "registry:ui", "title": "<PERSON><PERSON>", "description": "Displays a callout for user attention.", "registryDependencies": ["alert", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "avatar", "type": "registry:ui", "title": "Avatar", "description": "An image element with a fallback for representing the user.", "registryDependencies": ["avatar", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "badge", "type": "registry:ui", "title": "Badge", "description": "Displays a small count or status indicator.", "registryDependencies": ["badge", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "breadcrumb", "type": "registry:ui", "title": "Breadcrumb", "description": "Displays the path to the current resource using a hierarchy of links.", "registryDependencies": ["breadcrumb", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "button", "type": "registry:ui", "title": "<PERSON><PERSON>", "description": "Allows users to take actions with a single click or tap.", "registryDependencies": ["button", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "calendar", "type": "registry:ui", "title": "Calendar", "description": "A date field component that allows users to enter and edit date.", "registryDependencies": ["calendar", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "card", "type": "registry:ui", "title": "Card", "description": "Containers for displaying content and actions about a single subject.", "registryDependencies": ["card", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "chart", "type": "registry:ui", "title": "Chart", "description": "Beautiful charts. Built using Recharts. Copy and paste into your apps.", "registryDependencies": ["chart", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "checkbox", "type": "registry:ui", "title": "Checkbox", "description": "Allows users to select multiple items from a list of options.", "registryDependencies": ["checkbox", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "data-table", "type": "registry:ui", "title": "Data Table", "description": "Powerful table and datagrids built using TanStack Table.", "registryDependencies": ["data-table", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "date-picker", "type": "registry:ui", "title": "Date Picker", "description": "Displays the path to the current resource using a hierarchy of links.", "registryDependencies": ["date-picker", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "dialog", "type": "registry:ui", "title": "Dialog", "description": "A modal dialog that interrupts the user with important content.", "registryDependencies": ["dialog", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "dropdown-menu", "type": "registry:ui", "title": "Dropdown", "description": "Displays a menu to the user triggered by a button.", "registryDependencies": ["dropdown-menu", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "input", "type": "registry:ui", "title": "Input", "description": "Displays a form input field or a component that looks like an input field.", "registryDependencies": ["input", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "menu-bar", "type": "registry:ui", "title": "Menu Bar", "description": "A visually persistent menu common in desktop applications that provides quick access to a consistent set of commands.", "registryDependencies": ["menu-bar", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "select", "type": "registry:ui", "title": "Select", "description": "Displays a list of options for the user to pick from—triggered by a button.", "registryDependencies": ["select", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "separator", "type": "registry:ui", "title": "Separator", "description": "Visually or semantically separates content.", "registryDependencies": ["separator", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "skeleton", "type": "registry:ui", "title": "Skeleton", "description": "Use to show a placeholder while content is loading.", "registryDependencies": ["skeleton", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "slider", "type": "registry:ui", "title": "Slide<PERSON>", "description": "An input where the user selects a value from within a given range.", "registryDependencies": ["slider", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "sonner", "type": "registry:ui", "title": "<PERSON><PERSON>", "description": "An opinionated toast component for React.", "registryDependencies": ["sonner", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/components/ui/sonner.tsx", "type": "registry:file", "target": "components/ui/sonner.tsx"}]}, {"name": "switch", "type": "registry:ui", "title": "Switch", "description": "A control that allows the user to toggle between checked and not checked.", "registryDependencies": ["switch", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "table", "type": "registry:ui", "title": "Table", "description": "A responsive table component.", "registryDependencies": ["table", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "tabs", "type": "registry:ui", "title": "Tabs", "description": "A set of layered sections of content—known as tab panels—that are displayed one at a time.", "registryDependencies": ["tabs", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "toggle-group", "type": "registry:ui", "title": "Toggle Group", "description": "A popup that displays information related to an element when the element receives keyboard focus or the mouse hovers over it.", "registryDependencies": ["toggle-group", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "tooltip", "type": "registry:ui", "title": "<PERSON><PERSON><PERSON>", "description": "A popup that displays information related to an element when the element receives keyboard focus or the mouse hovers over it.", "registryDependencies": ["tooltip", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}]}