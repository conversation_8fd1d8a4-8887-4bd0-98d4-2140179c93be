# PowerShell script to fix component import paths
$blocksPath = "src\app\demo\[name]\blocks"

# Component mapping from incorrect to correct paths
$componentMap = @{
    "'@/components/Accordion'" = "'@/components/ui/accordion'"
    "'@/components/AlertDialog'" = "'@/components/ui/alert-dialog'"
    "'@/components/Alert'" = "'@/components/ui/alert'"
    "'@/components/AspectRatio'" = "'@/components/ui/aspect-ratio'"
    "'@/components/Avatar'" = "'@/components/ui/avatar'"
    "'@/components/Badge'" = "'@/components/ui/badge'"
    "'@/components/Breadcrumb'" = "'@/components/ui/breadcrumb'"
    "'@/components/Button'" = "'@/components/ui/button'"
    "'@/components/Calendar'" = "'@/components/ui/calendar'"
    "'@/components/Card'" = "'@/components/ui/card'"
    "'@/components/Carousel'" = "'@/components/ui/carousel'"
    "'@/components/Chart'" = "'@/components/ui/chart'"
    "'@/components/Checkbox'" = "'@/components/ui/checkbox'"
    "'@/components/Collapsible'" = "'@/components/ui/collapsible'"
    "'@/components/Command'" = "'@/components/ui/command'"
    "'@/components/ContextMenu'" = "'@/components/ui/context-menu'"
    "'@/components/Dialog'" = "'@/components/ui/dialog'"
    "'@/components/Drawer'" = "'@/components/ui/drawer'"
    "'@/components/DropdownMenu'" = "'@/components/ui/dropdown-menu'"
    "'@/components/Form'" = "'@/components/ui/form'"
    "'@/components/HoverCard'" = "'@/components/ui/hover-card'"
    "'@/components/InputOtp'" = "'@/components/ui/input-otp'"
    "'@/components/Input'" = "'@/components/ui/input'"
    "'@/components/Label'" = "'@/components/ui/label'"
    "'@/components/Menubar'" = "'@/components/ui/menubar'"
    "'@/components/NavigationMenu'" = "'@/components/ui/navigation-menu'"
    "'@/components/Pagination'" = "'@/components/ui/pagination'"
    "'@/components/Popover'" = "'@/components/ui/popover'"
    "'@/components/Progress'" = "'@/components/ui/progress'"
    "'@/components/RadioGroup'" = "'@/components/ui/radio-group'"
    "'@/components/Resizable'" = "'@/components/ui/resizable'"
    "'@/components/ScrollArea'" = "'@/components/ui/scroll-area'"
    "'@/components/Select'" = "'@/components/ui/select'"
    "'@/components/Separator'" = "'@/components/ui/separator'"
    "'@/components/Sheet'" = "'@/components/ui/sheet'"
    "'@/components/Sidebar'" = "'@/components/ui/sidebar'"
    "'@/components/Skeleton'" = "'@/components/ui/skeleton'"
    "'@/components/Slider'" = "'@/components/ui/slider'"
    "'@/components/Sonner'" = "'@/components/ui/sonner'"
    "'@/components/Switch'" = "'@/components/ui/switch'"
    "'@/components/Table'" = "'@/components/ui/table'"
    "'@/components/Tabs'" = "'@/components/ui/tabs'"
    "'@/components/Textarea'" = "'@/components/ui/textarea'"
    "'@/components/ToggleGroup'" = "'@/components/ui/toggle-group'"
    "'@/components/Toggle'" = "'@/components/ui/toggle'"
    "'@/components/Tooltip'" = "'@/components/ui/tooltip'"
    "'@/components/Divider'" = "'@/components/ui/separator'"
    "'@/components/AreaChart'" = "'@/components/ui/chart'"
    "'@/components/BarChart'" = "'@/components/ui/chart'"
    "'@/components/DonutChart'" = "'@/components/ui/chart'"
    "'@/components/LineChart'" = "'@/components/ui/chart'"
    "'@/components/PieChart'" = "'@/components/ui/chart'"
    "'@/components/RadarChart'" = "'@/components/ui/chart'"
    "'@/components/RadialChart'" = "'@/components/ui/chart'"
    "'@/components/ScatterChart'" = "'@/components/ui/chart'"
}

# Get all TypeScript files in the blocks directory
$files = Get-ChildItem -Path $blocksPath -Filter "*.tsx" -Recurse

Write-Host "Found $($files.Count) files to process..."

foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw
    $originalContent = $content
    
    # Apply each component mapping
    foreach ($oldPath in $componentMap.Keys) {
        $newPath = $componentMap[$oldPath]
        $content = $content -replace [regex]::Escape($oldPath), $newPath
    }
    
    # Only write if content changed
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "Fixed imports in: $($file.Name)"
    }
}

Write-Host "Import fixing complete!"
