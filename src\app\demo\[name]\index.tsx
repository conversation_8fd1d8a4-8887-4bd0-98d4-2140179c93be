import type { ReactElement, ReactNode } from "react";
import { lazy } from "react";

// blocks
import { blank } from "@/app/demo/[name]/blocks/blank";
import { dashboard } from "@/app/demo/[name]/blocks/dashboard";
import { store } from "@/app/demo/[name]/blocks/store";

// Import all block components
import * as BlockComponents from "@/app/demo/[name]/block-imports";

// components
import { brandHeader } from "@/app/demo/[name]/components/brand-header";
import { brandSidebar } from "@/app/demo/[name]/components/brand-sidebar";
import { hero } from "@/app/demo/[name]/components/hero";
import { login } from "@/app/demo/[name]/components/login";
import { logo } from "@/app/demo/[name]/components/logo";
import { productGrid } from "@/app/demo/[name]/components/product-grid";
import { promo } from "@/app/demo/[name]/components/promo";

// ui
import { accordion } from "@/app/demo/[name]/ui/accordion";
import { alert } from "@/app/demo/[name]/ui/alert";
import { avatar } from "@/app/demo/[name]/ui/avatar";
import { badge } from "@/app/demo/[name]/ui/badge";
import { breadcrumb } from "@/app/demo/[name]/ui/breadcrumb";
import { button } from "@/app/demo/[name]/ui/button";
import { calendar } from "@/app/demo/[name]/ui/calendar";
import { card } from "@/app/demo/[name]/ui/card";
import { chart } from "@/app/demo/[name]/ui/chart";
import { checkbox } from "@/app/demo/[name]/ui/checkbox";
import { dataTable } from "@/app/demo/[name]/ui/data-table";
import { datePicker } from "@/app/demo/[name]/ui/date-picker";
import { dialog } from "@/app/demo/[name]/ui/dialog";
import { dropdownMenu } from "@/app/demo/[name]/ui/dropdown-menu";
import { input } from "@/app/demo/[name]/ui/input";
import { menuBar } from "@/app/demo/[name]/ui/menu-bar";
import { select } from "@/app/demo/[name]/ui/select";
import { separator } from "@/app/demo/[name]/ui/separator";
import { skeleton } from "@/app/demo/[name]/ui/skeleton";
import { slider } from "@/app/demo/[name]/ui/slider";
import { sonner } from "@/app/demo/[name]/ui/sonner";
import { switchComponent } from "@/app/demo/[name]/ui/switch";
import { table } from "@/app/demo/[name]/ui/table";
import { tabs } from "@/app/demo/[name]/ui/tabs";
import { toggleGroup } from "@/app/demo/[name]/ui/toggle-group";
import { tooltip } from "@/app/demo/[name]/ui/tooltip";

interface Demo {
  name: string; // this must match the `registry.json` name
  components?: {
    [name: string]: ReactNode | ReactElement;
  };
}

// List of all block names that should have demo objects
const blockNames = [
  'account-and-user-management-01', 'account-and-user-management-02', 'account-and-user-management-03',
  'account-and-user-management-04', 'account-and-user-management-05', 'account-and-user-management-06',
  'account-and-user-management-07', 'account-and-user-management-08', 'account-and-user-management-09',
  'area-chart-01', 'area-chart-02', 'area-chart-03', 'area-chart-04', 'area-chart-05', 'area-chart-06',
  'area-chart-07', 'area-chart-08', 'area-chart-09', 'area-chart-10', 'area-chart-11', 'area-chart-12',
  'area-chart-13', 'area-chart-14', 'area-chart-15', 'area-chart-16',
  'banner-01', 'banner-02', 'banner-03', 'banner-04', 'banner-05',
  'bar-chart-01', 'bar-chart-02', 'bar-chart-03', 'bar-chart-04', 'bar-chart-05', 'bar-chart-06',
  'bar-chart-07', 'bar-chart-08', 'bar-chart-09', 'bar-chart-10', 'bar-chart-11', 'bar-chart-12',
  'bar-list-01', 'bar-list-02', 'bar-list-03', 'bar-list-04', 'bar-list-05', 'bar-list-06', 'bar-list-07',
  'billing-usage-01', 'billing-usage-02', 'billing-usage-03', 'billing-usage-04', 'billing-usage-05',
  'billing-usage-06', 'billing-usage-07', 'billing-usage-08', 'billing-usage-09', 'billing-usage-10',
  'chart-composition-01', 'chart-composition-02', 'chart-composition-03', 'chart-composition-04', 'chart-composition-05',
  'chart-composition-06', 'chart-composition-07', 'chart-composition-08', 'chart-composition-09', 'chart-composition-10',
  'chart-composition-11', 'chart-composition-12', 'chart-composition-13', 'chart-composition-14', 'chart-composition-15',
  'chart-tooltip-01', 'chart-tooltip-02', 'chart-tooltip-03', 'chart-tooltip-04', 'chart-tooltip-05',
  'chart-tooltip-06', 'chart-tooltip-07', 'chart-tooltip-08', 'chart-tooltip-09', 'chart-tooltip-10',
  'chart-tooltip-11', 'chart-tooltip-12', 'chart-tooltip-13', 'chart-tooltip-14', 'chart-tooltip-15',
  'chart-tooltip-16', 'chart-tooltip-17', 'chart-tooltip-18', 'chart-tooltip-19', 'chart-tooltip-20', 'chart-tooltip-21',
  'dialog-01', 'dialog-02', 'dialog-03', 'dialog-04', 'dialog-05', 'dialog-06', 'dialog-07', 'dialog-08', 'dialog-09',
  'donut-chart-01', 'donut-chart-02', 'donut-chart-03', 'donut-chart-04', 'donut-chart-05', 'donut-chart-06', 'donut-chart-07',
  'empty-state-01', 'empty-state-02', 'empty-state-03', 'empty-state-04', 'empty-state-05',
  'empty-state-06', 'empty-state-07', 'empty-state-08', 'empty-state-09', 'empty-state-10',
  'feature-section-01', 'feature-section-02', 'feature-section-03', 'feature-section-04', 'feature-section-05',
  'feature-section-06', 'feature-section-07', 'feature-section-08', 'feature-section-09', 'feature-section-10',
  'grid-list-01', 'grid-list-02', 'grid-list-03', 'grid-list-04', 'grid-list-05', 'grid-list-06', 'grid-list-07',
  'grid-list-08', 'grid-list-09', 'grid-list-10', 'grid-list-11', 'grid-list-12', 'grid-list-13', 'grid-list-14', 'grid-list-15',
  'login-01', 'login-02', 'login-03', 'login-04', 'login-05', 'login-06', 'login-07', 'login-08', 'login-09', 'login-10',
  'onboarding-feed-01', 'onboarding-feed-02', 'onboarding-feed-03', 'onboarding-feed-04', 'onboarding-feed-05',
  'onboarding-feed-06', 'onboarding-feed-07', 'onboarding-feed-08', 'onboarding-feed-09', 'onboarding-feed-10',
  'onboarding-feed-11', 'onboarding-feed-12', 'onboarding-feed-13', 'onboarding-feed-14', 'onboarding-feed-15', 'onboarding-feed-16',
  'table-01', 'table-02', 'table-03', 'table-04', 'table-05', 'table-06', 'table-07', 'table-08', 'table-09', 'table-10', 'table-11',
  'table-action-01', 'table-action-02', 'table-action-03', 'table-action-04', 'table-action-05', 'table-action-06',
  'table-action-07', 'table-action-08', 'table-action-09', 'table-action-10', 'table-action-11',
  'tracker-01', 'tracker-02', 'tracker-03', 'tracker-04', 'tracker-05', 'tracker-06', 'tracker-07', 'tracker-08', 'tracker-09', 'tracker-10',
  'workspace-usage'
];

// Function to convert kebab-case to PascalCase
function toPascalCase(str: string): string {
  return str.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('');
}

// Generate dynamic demo objects for all blocks
const dynamicDemos: { [name: string]: Demo } = {};
blockNames.forEach(blockName => {
  const componentName = toPascalCase(blockName);
  const Component = (BlockComponents as any)[componentName];
  if (Component) {
    dynamicDemos[blockName] = {
      name: blockName,
      components: { Default: <Component /> }
    };
  }
});

export const demos: { [name: string]: Demo } = {
  // blocks
  blank,
  store,
  dashboard,
  
  // dynamically generated demos for all blocks
  ...dynamicDemos,

  // components
  hero,
  login,
  promo,
  logo,
  "brand-header": brandHeader,
  "brand-sidebar": brandSidebar,
  "product-grid": productGrid,

  // ui
  accordion,
  alert,
  avatar,
  badge,
  breadcrumb,
  button,
  calendar,
  card,
  chart,
  checkbox,
  dialog,
  "date-picker": datePicker,
  "data-table": dataTable,
  "dropdown-menu": dropdownMenu,
  input,
  "menu-bar": menuBar,
  select,
  separator,
  skeleton,
  slider,
  switch: switchComponent,
  sonner,
  table,
  tabs,
  "toggle-group": toggleGroup,
  tooltip,
};
